package eve.sys.modular.bombill.positivematerialaccounting.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.exception.ServiceException;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bombill.chemicalelement.entity.ChemicalElement;
import eve.sys.modular.bombill.chemicalelement.service.IChemicalElementService;
import eve.sys.modular.bombill.positivematerialaccounting.dto.PositiveMaterialAccountingExcelDto;
import eve.sys.modular.bombill.positivematerialaccounting.entity.PositiveMaterialAccounting;
import eve.sys.modular.bombill.positivematerialaccounting.excel.PositiveMaterialAccountingExcelHandler;
import eve.sys.modular.bombill.positivematerialaccounting.mapper.PositiveMaterialAccountingMapper;
import eve.sys.modular.bombill.positivematerialaccounting.param.PositiveMaterialAccountingCheckDuplicateParam;
import eve.sys.modular.bombill.positivematerialaccounting.param.PositiveMaterialImportParam;
import eve.sys.modular.bombill.positivematerialaccounting.service.IPositiveMaterialAccountingService;
import eve.sys.modular.bombill.positivematerialaccounting.util.PositiveMaterialAccountingExcelUtil;
import eve.sys.modular.bombill.scenario.entity.Scenario;
import eve.sys.modular.bombill.scenario.service.IScenarioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PositiveMaterialAccountingServiceImpl extends ServiceImpl<PositiveMaterialAccountingMapper, PositiveMaterialAccounting>
    implements IPositiveMaterialAccountingService {

    @Resource
    private IChemicalElementService chemicalElementService;

    @Override
    public PageResult<JSONObject> pageList(PositiveMaterialAccounting param) {
        LambdaQueryWrapper<PositiveMaterialAccounting> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StrUtil.isNotBlank(param.getChemicalSystemCode())) {
            queryWrapper.like(PositiveMaterialAccounting::getChemicalSystemCode, param.getChemicalSystemCode());
        }
        
        if (StrUtil.isNotBlank(param.getChemicalSystem())) {
            queryWrapper.like(PositiveMaterialAccounting::getChemicalSystem, param.getChemicalSystem());
        }
        
        queryWrapper.orderByDesc(PositiveMaterialAccounting::getCreateTime);
        Page<PositiveMaterialAccounting> page = this.page(new Page<>(param.getPageNo(), param.getPageSize()), queryWrapper);
        
        List<JSONObject> jsonObjects = new ArrayList<>();

        page.getRecords().forEach(item -> {

            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(item));
            JSONObject _obj = JSONObject.parseObject(item.getChemicalManagementElementsJson());
            
            for (Map.Entry<String,Object> entry : _obj.entrySet()) {
                obj.put(entry.getKey(), entry.getValue());
            }

            jsonObjects.add(obj);
        });

        Page<JSONObject> pageResult = new Page<>();
        pageResult.setRecords(jsonObjects);
        pageResult.setPages(page.getPages());
        pageResult.setTotal(page.getTotal());
        pageResult.setCurrent(page.getCurrent());
        pageResult.setSize(page.getSize());
        
        return new PageResult<>(pageResult);
    }

    @Override
    public List<JSONObject> list(PositiveMaterialAccounting param) {


        LambdaQueryWrapper<PositiveMaterialAccounting> queryWrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(param.getChemicalSystemCode())) {
            queryWrapper.like(PositiveMaterialAccounting::getChemicalSystemCode, param.getChemicalSystemCode());
        }

        if (StrUtil.isNotBlank(param.getChemicalSystem())) {
            queryWrapper.like(PositiveMaterialAccounting::getChemicalSystem, param.getChemicalSystem());
        }

        queryWrapper.orderByDesc(PositiveMaterialAccounting::getCreateTime);

        List<PositiveMaterialAccounting> list = this.list(queryWrapper);

        List<JSONObject> jsonObjects = new ArrayList<>();

        list.forEach(item->{

            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(item));
            JSONObject _obj = JSONObject.parseObject(item.getChemicalManagementElementsJson());
            
            for (Map.Entry<String,Object> entry : _obj.entrySet()) {
                obj.put(entry.getKey(), entry.getValue());
            }
            jsonObjects.add(obj);
        });

        return jsonObjects;
    }

    @Override
    public List<JSONObject> listByChemicalSystemCodes(Set<String> chemicalSystemCodes) {
        if (chemicalSystemCodes == null || chemicalSystemCodes.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<PositiveMaterialAccounting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PositiveMaterialAccounting::getChemicalSystemCode, chemicalSystemCodes);
        queryWrapper.orderByDesc(PositiveMaterialAccounting::getCreateTime);

        List<PositiveMaterialAccounting> list = this.list(queryWrapper);

        List<JSONObject> jsonObjects = new ArrayList<>();

        list.forEach(item->{
            JSONObject obj = JSONObject.parseObject(JSONObject.toJSONString(item));
            JSONObject _obj = JSONObject.parseObject(item.getChemicalManagementElementsJson());

            for (Map.Entry<String,Object> entry : _obj.entrySet()) {
                obj.put(entry.getKey(), entry.getValue());
            }
            jsonObjects.add(obj);
        });

        return jsonObjects;
    }

    @Override
    public Boolean add(PositiveMaterialAccounting param) {
        
        param.setChemicalSystemCode(param.getChemicalSystem()+"-"+String.format("%03d", this.count(
            Wrappers.lambdaQuery(PositiveMaterialAccounting.class)
            .eq(PositiveMaterialAccounting::getChemicalSystem, param.getChemicalSystem())
        )+1));
        return this.save(param);
    }
    
    @Resource
    private IScenarioService scenarioService;

    @Override
    public Boolean delete(PositiveMaterialAccounting param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        if (scenarioService.count(
            Wrappers.lambdaQuery(Scenario.class)
            .eq(Scenario::getPositiveMaterialAccountingId, param.getId())
        ) > 0){
            throw new ServiceException(500, "该正极材料核算已被使用，不可删除");
        }
        return this.removeById(param.getId());
    }

    @Override
    public Boolean update(PositiveMaterialAccounting param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        //PositiveMaterialAccounting entity = this.getById(param.getId());
        PositiveMaterialAccounting updateEntity = PositiveMaterialAccounting.builder()
            .id(param.getId())
            .chemicalSystem(param.getChemicalSystem())
            .processingFee(param.getProcessingFee())
            .chemicalManagementElementsJson(param.getChemicalManagementElementsJson())
            .build();
        updateEntity.setChemicalSystemCode(param.getChemicalSystem()+"-"+String.format("%03d", this.count(
            Wrappers.lambdaQuery(PositiveMaterialAccounting.class)
            .eq(PositiveMaterialAccounting::getChemicalSystem, param.getChemicalSystem())
            .ne(PositiveMaterialAccounting::getId, param.getId())
        )+1));
        //updateEntity.setChemicalSystemCode(entity.getChemicalSystemCode().replace(entity.getChemicalSystem(), updateEntity.getChemicalSystem()));
        return this.updateById(updateEntity);
    }

    @Override
    public PositiveMaterialAccounting get(PositiveMaterialAccounting param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.getById(param.getId());
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try {
            log.info("开始下载正极材料核算导入模板");

            // 1. 获取化学元素列表
            List<ChemicalElement> chemicalElements = chemicalElementService.list(new ChemicalElement());
            log.info("模板下载 - 获取到化学元素列表，数量: {}", chemicalElements.size());

            // 2. 生成完整表头
            List<List<String>> completeHeads = PositiveMaterialAccountingExcelUtil.generateCompleteHeads(chemicalElements);
            log.info("模板下载 - 生成表头，列数: {}", completeHeads.size());

            // 3. 生成核算类型元素映射
            Map<String, List<String>> accountingTypeElements = PositiveMaterialAccountingExcelUtil.generateAccountingTypeElements(chemicalElements);

            // 4. 生成文件名
            String fileName = "正极材料核算导入模板_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";

            // 5. 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" +
                URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

            // 6. 创建列宽处理器
            int baseColumnCount = 3; // 基础列数量（化学体系编号、化学体系、加工费）
            PositiveMaterialAccountingExcelHandler excelHandler = new PositiveMaterialAccountingExcelHandler(baseColumnCount, accountingTypeElements);

            // 7. 写入Excel模板（空数据）
            EasyExcel.write(response.getOutputStream())
                    .head(completeHeads)
                    .registerWriteHandler(excelHandler)
                    .sheet("正极材料核算")
                    .doWrite(new ArrayList<>());

            log.info("正极材料核算导入模板下载成功");

        } catch (Exception e) {
            log.error("正极材料核算导入模板下载失败", e);

            // 下载失败时返回错误信息
            try {
                response.reset();
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"模板下载失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

    @Override
    public List<PositiveMaterialAccountingExcelDto> importExcel(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException(400, "上传文件不能为空");
        }

        try {
            log.info("开始导入正极材料核算Excel，文件名: {}", file.getOriginalFilename());

            // 1. 获取化学元素列表
            List<ChemicalElement> chemicalElements = chemicalElementService.list(new ChemicalElement());
            log.info("获取到化学元素列表，数量: {}", chemicalElements.size());
            for (ChemicalElement element : chemicalElements) {
                log.debug("化学元素: {} - {} (核算类型: {})", element.getId(), element.getElementName(), element.getAccountingType());
            }

            // 2. 读取Excel数据（不指定表头，直接读取原始数据）
            // 根据文件扩展名确定Excel类型
            String fileName = file.getOriginalFilename();
            com.alibaba.excel.support.ExcelTypeEnum excelType = com.alibaba.excel.support.ExcelTypeEnum.XLSX;
            if (fileName != null && fileName.toLowerCase().endsWith(".xls")) {
                excelType = com.alibaba.excel.support.ExcelTypeEnum.XLS;
            }

            List<Map<Integer, String>> allData = EasyExcel.read(file.getInputStream())
                    .excelType(excelType) // 明确指定Excel类型
                    .sheet()
                    .headRowNumber(0) // 不跳过表头，读取所有行
                    .doReadSync();

            log.info("正极材料核算Excel读取成功，共读取 {} 行（包含表头）", allData.size());

            // 打印前5行数据用于调试（包含表头）
            for (int i = 0; i < Math.min(5, allData.size()); i++) {
                log.info("第 {} 行数据（包含表头）: {}", i + 1, allData.get(i));
            }

            // 手动跳过前2行表头
            List<Map<Integer, String>> readData = new ArrayList<>();
            if (allData.size() > 2) {
                readData = allData.subList(2, allData.size());
                log.info("手动跳过前2行表头后，剩余数据行数: {}", readData.size());
            } else {
                log.warn("Excel文件行数不足，无法跳过表头");
            }

            // 4. 转换数据
            List<PositiveMaterialAccountingExcelDto> dataList = new ArrayList<>();
            for (Map<Integer, String> rowData : readData) {
                PositiveMaterialAccountingExcelDto dto = convertRowDataToDto(rowData, chemicalElements);

                // 数据验证
                String errorMessage = PositiveMaterialAccountingExcelUtil.validateExcelData(dto);
                dto.setErrorMessage(errorMessage);

                dataList.add(dto);
            }

            return dataList;

        } catch (Exception e) {
            log.error("正极材料核算Excel导入失败", e);
            throw new ServiceException(500, "Excel导入失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSaveImportData(PositiveMaterialImportParam param/* List<PositiveMaterialAccountingExcelDto> dataList */) {
        if (param.getData() == null || param.getData().isEmpty()) {
            return true;
        }

        try {
            log.info("开始批量保存正极材料核算数据（覆盖模式），共 {} 条", param.getData().size());

            // 1. 获取化学元素列表
            List<ChemicalElement> chemicalElements = chemicalElementService.list(new ChemicalElement());

            // 2. 检查导入数据内部重复（同一次导入中不允许有重复的化学体系编号）
            validateInternalDuplicates(param.getData());

            // 3. 检查是否有内部重复错误
            long internalDuplicateCount = param.getData().stream()
                .filter(dto -> StrUtil.isNotBlank(dto.getErrorMessage()) &&
                              (dto.getErrorMessage().contains("导入数据中重复")))
                .count();

            if (internalDuplicateCount > 0) {
                List<String> duplicateErrors = new ArrayList<>();
                for (PositiveMaterialAccountingExcelDto dto : param.getData()) {
                    if (StrUtil.isNotBlank(dto.getErrorMessage()) &&
                        dto.getErrorMessage().contains("导入数据中重复")) {
                        duplicateErrors.add(String.format("第%d行: %s",
                            param.getData().indexOf(dto) + 1, dto.getErrorMessage()));
                    }
                }
                String errorMessage = "导入数据内部存在重复的化学体系编号：\n" + String.join("\n", duplicateErrors);
                log.error("导入失败 - {}", errorMessage);
                throw new ServiceException(400, errorMessage);
            }

            // 4. 检查是否还有其他错误（排除数据库重复错误，因为我们要覆盖）
            long otherErrorCount = param.getData().stream()
                .filter(dto -> StrUtil.isNotBlank(dto.getErrorMessage()) &&
                              !dto.getErrorMessage().contains("在数据库中已存在"))
                .count();

            if (otherErrorCount > 0) {
                List<String> allErrors = new ArrayList<>();
                for (PositiveMaterialAccountingExcelDto dto : param.getData()) {
                    if (StrUtil.isNotBlank(dto.getErrorMessage()) &&
                        !dto.getErrorMessage().contains("在数据库中已存在")) {
                        allErrors.add(String.format("第%d行: %s",
                            param.getData().indexOf(dto) + 1, dto.getErrorMessage()));
                    }
                }
                String errorMessage = "数据验证失败，请修正以下错误后重新导入：\n" + String.join("\n", allErrors);
                log.error("导入失败 - {}", errorMessage);
                throw new ServiceException(400, errorMessage);
            }

            // 5. 处理覆盖导入逻辑
            return processOverrideImport(param.getData(), chemicalElements);

        } catch (Exception e) {
            log.error("正极材料核算数据批量保存失败", e);
            throw new ServiceException(500, "批量保存失败：" + e.getMessage());
        }
    }

    /**
     * 检查导入数据内部重复
     */
    private void validateInternalDuplicates(List<PositiveMaterialAccountingExcelDto> dataList) {
        log.info("开始检查导入数据内部重复，数据量: {}", dataList.size());

        Map<String, Integer> codeCountMap = new HashMap<>();
        Map<String, List<Integer>> codeRowsMap = new HashMap<>();

        for (int i = 0; i < dataList.size(); i++) {
            PositiveMaterialAccountingExcelDto dto = dataList.get(i);
            String chemicalSystemCode = dto.getChemicalSystemCode();

            if (StrUtil.isNotBlank(chemicalSystemCode)) {
                codeCountMap.put(chemicalSystemCode, codeCountMap.getOrDefault(chemicalSystemCode, 0) + 1);
                codeRowsMap.computeIfAbsent(chemicalSystemCode, k -> new ArrayList<>()).add(i + 1);
            }
        }

        // 标记导入数据内部重复的记录
        for (Map.Entry<String, Integer> entry : codeCountMap.entrySet()) {
            if (entry.getValue() > 1) {
                String duplicateCode = entry.getKey();
                List<Integer> rows = codeRowsMap.get(duplicateCode);
                log.warn("导入数据中发现重复的化学体系编号: {}, 出现在第 {} 行", duplicateCode, rows);

                // 为所有重复的行添加错误信息
                for (Integer rowIndex : rows) {
                    PositiveMaterialAccountingExcelDto dto = dataList.get(rowIndex - 1);
                    String existingError = dto.getErrorMessage();
                    String newError = String.format("化学体系编号 '%s' 在导入数据中重复（共出现在第 %s 行）",
                        duplicateCode, rows.stream().map(String::valueOf).collect(Collectors.joining(", ")));

                    if (StrUtil.isNotBlank(existingError)) {
                        dto.setErrorMessage(existingError + "; " + newError);
                    } else {
                        dto.setErrorMessage(newError);
                    }
                }
            }
        }
    }

    /**
     * 处理覆盖导入逻辑
     */
    private Boolean processOverrideImport(List<PositiveMaterialAccountingExcelDto> dataList,
                                        List<ChemicalElement> chemicalElements) {
        try {
            // 1. 提取所有化学体系编号
            List<String> chemicalSystemCodes = dataList.stream()
                .map(PositiveMaterialAccountingExcelDto::getChemicalSystemCode)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

            // 2. 查询数据库中已存在的记录
            LambdaQueryWrapper<PositiveMaterialAccounting> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(PositiveMaterialAccounting::getChemicalSystemCode, chemicalSystemCodes);
            List<PositiveMaterialAccounting> existingRecords = this.list(queryWrapper);

            // 3. 构建化学体系编号到记录的映射
            Map<String, PositiveMaterialAccounting> existingMap = existingRecords.stream()
                .collect(Collectors.toMap(
                    PositiveMaterialAccounting::getChemicalSystemCode,
                    record -> record,
                    (existing, replacement) -> existing
                ));

            // 4. 分类处理：新增和更新
            List<PositiveMaterialAccounting> toInsert = new ArrayList<>();
            List<PositiveMaterialAccounting> toUpdate = new ArrayList<>();
            int coverCount = 0;

            for (PositiveMaterialAccountingExcelDto dto : dataList) {
                if (StrUtil.isBlank(dto.getChemicalSystemCode())) {
                    continue;
                }

                PositiveMaterialAccounting entity = PositiveMaterialAccountingExcelUtil.toEntity(dto, chemicalElements);
                PositiveMaterialAccounting existing = existingMap.get(dto.getChemicalSystemCode());

                if (existing != null) {
                    // 存在相同化学体系编号，准备覆盖更新
                    entity.setId(existing.getId());
                    entity.setCreateTime(existing.getCreateTime());
                    entity.setCreateUser(existing.getCreateUser());
                    toUpdate.add(entity);
                    coverCount++;
                } else {
                    // 不存在相同化学体系编号，准备新增
                    toInsert.add(entity);
                }
            }

            boolean result = true;

            // 5. 批量新增
            if (!toInsert.isEmpty()) {
                List<List<PositiveMaterialAccounting>> insertPartitions =
                    com.google.common.collect.Lists.partition(toInsert, 100);
                for (List<PositiveMaterialAccounting> partition : insertPartitions) {
                    result = result && this.saveBatch(partition);
                }
            }

            // 6. 批量更新
            if (!toUpdate.isEmpty()) {
                List<List<PositiveMaterialAccounting>> updatePartitions =
                    com.google.common.collect.Lists.partition(toUpdate, 100);
                for (List<PositiveMaterialAccounting> partition : updatePartitions) {
                    result = result && this.updateBatchById(partition);
                }
            }

            log.info("正极材料核算数据覆盖导入完成，共处理 {} 条数据，其中新增 {} 条，覆盖更新 {} 条",
                dataList.size(), toInsert.size(), coverCount);

            return result;

        } catch (Exception e) {
            log.error("覆盖导入处理失败", e);
            throw new ServiceException(500, "覆盖导入处理失败：" + e.getMessage());
        }
    }

    /**
     * 将行数据转换为DTO
     */
    private PositiveMaterialAccountingExcelDto convertRowDataToDto(Map<Integer, String> rowData, List<ChemicalElement> chemicalElements) {
        log.debug("开始转换行数据，rowData: {}", rowData);
        log.debug("化学元素列表大小: {}", chemicalElements.size());

        PositiveMaterialAccountingExcelDto dto = new PositiveMaterialAccountingExcelDto();
        List<String> errors = new ArrayList<>();

        // 基础字段验证和设置
        String chemicalSystemCode = rowData.get(0);
        String chemicalSystem = rowData.get(1);

        // 验证化学体系编号
        if (StrUtil.isBlank(chemicalSystemCode)) {
            errors.add("化学体系编号不能为空");
        } else {
            // 检查化学体系编号格式（可以根据需要添加更多验证规则）
            if (chemicalSystemCode.trim().length() > 50) {
                errors.add("化学体系编号长度不能超过50个字符");
            }
        }

        // 验证化学体系名称
        if (StrUtil.isBlank(chemicalSystem)) {
            errors.add("化学体系名称不能为空");
        } else if (chemicalSystem.trim().length() > 100) {
            errors.add("化学体系名称长度不能超过100个字符");
        }

        dto.setChemicalSystemCode(chemicalSystemCode);
        dto.setChemicalSystem(chemicalSystem);
        log.debug("基础字段 - 化学体系编号: {}, 化学体系: {}", chemicalSystemCode, chemicalSystem);

        String processingFeeStr = rowData.get(2);
        if (StrUtil.isNotBlank(processingFeeStr)) {
            try {
                dto.setProcessingFee(new BigDecimal(processingFeeStr));
                log.debug("基础字段 - 加工费: {}", processingFeeStr);
            } catch (NumberFormatException e) {
                log.warn("加工费格式错误: {}", processingFeeStr);
            }
        }

        // 动态元素数据
        Map<String, BigDecimal> elementData = new HashMap<>();
        int columnIndex = 3; // 从第4列开始是动态元素数据

        // 按照生成表头时的顺序来读取元素数据
        // 需要按核算类型分组，然后按元素顺序读取
        Map<String, List<ChemicalElement>> groupByAccountingType = chemicalElements.stream()
                .collect(Collectors.groupingBy(ChemicalElement::getAccountingType));

        log.debug("核算类型分组: {}", groupByAccountingType.keySet());

        // 按核算类型排序处理
        for (String accountingType : groupByAccountingType.keySet().stream().sorted().collect(Collectors.toList())) {
            List<ChemicalElement> elements = groupByAccountingType.get(accountingType);
            log.debug("处理核算类型: {}, 元素数量: {}", accountingType, elements.size());

            for (ChemicalElement element : elements) {
                String key = element.getAccountingType() + "_" + element.getElementName();
                String valueStr = rowData.get(columnIndex);

                log.debug("列索引: {}, 元素key: {}, 值: {}", columnIndex, key, valueStr);

                if (StrUtil.isNotBlank(valueStr)) {
                    try {
                        BigDecimal value = new BigDecimal(valueStr);
                        elementData.put(key, value);
                        log.debug("成功添加元素数据: {} = {}", key, value);
                    } catch (NumberFormatException e) {
                        log.warn("元素用量格式错误: {} = {}", key, valueStr);
                    }
                } else {
                    log.debug("元素值为空，跳过: {}", key);
                }

                columnIndex++;
            }
        }

        log.debug("最终元素数据: {}", elementData);

        dto.setElementData(elementData);

        // 设置错误信息
        if (!errors.isEmpty()) {
            dto.setErrorMessage(String.join("; ", errors));
            log.warn("行数据验证失败: {}", dto.getErrorMessage());
        }

        return dto;
    }

    /**
     * 验证化学体系编号是否重复
     * 包括：1. 导入数据内部重复检查  2. 与数据库现有数据重复检查
     */
    private void validateChemicalSystemCodes(List<PositiveMaterialAccountingExcelDto> dataList) {
        log.info("开始验证化学体系编号重复性，数据量: {}", dataList.size());

        // 1. 检查导入数据内部是否有重复的化学体系编号
        Map<String, Integer> codeCountMap = new HashMap<>();
        Map<String, List<Integer>> codeRowsMap = new HashMap<>();

        for (int i = 0; i < dataList.size(); i++) {
            PositiveMaterialAccountingExcelDto dto = dataList.get(i);
            String chemicalSystemCode = dto.getChemicalSystemCode();

            if (StrUtil.isNotBlank(chemicalSystemCode)) {
                codeCountMap.put(chemicalSystemCode, codeCountMap.getOrDefault(chemicalSystemCode, 0) + 1);
                codeRowsMap.computeIfAbsent(chemicalSystemCode, k -> new ArrayList<>()).add(i + 1); // 行号从1开始
            }
        }

        // 标记导入数据内部重复的记录
        for (Map.Entry<String, Integer> entry : codeCountMap.entrySet()) {
            if (entry.getValue() > 1) {
                String duplicateCode = entry.getKey();
                List<Integer> rows = codeRowsMap.get(duplicateCode);
                log.warn("导入数据中发现重复的化学体系编号: {}, 出现在第 {} 行", duplicateCode, rows);

                // 为所有重复的行添加错误信息
                for (Integer rowIndex : rows) {
                    PositiveMaterialAccountingExcelDto dto = dataList.get(rowIndex - 1); // 转换为数组索引
                    String existingError = dto.getErrorMessage();
                    String newError = String.format("化学体系编号 '%s' 在导入数据中重复（共出现在第 %s 行）",
                        duplicateCode, rows.stream().map(String::valueOf).collect(Collectors.joining(", ")));

                    if (StrUtil.isNotBlank(existingError)) {
                        dto.setErrorMessage(existingError + "; " + newError);
                    } else {
                        dto.setErrorMessage(newError);
                    }
                }
            }
        }

        // 2. 检查与数据库现有数据是否重复
        Set<String> importCodes = dataList.stream()
            .map(PositiveMaterialAccountingExcelDto::getChemicalSystemCode)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());

        if (!importCodes.isEmpty()) {
            // 查询数据库中已存在的化学体系编号
            LambdaQueryWrapper<PositiveMaterialAccounting> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(PositiveMaterialAccounting::getChemicalSystemCode, importCodes);
            queryWrapper.select(PositiveMaterialAccounting::getChemicalSystemCode);

            List<PositiveMaterialAccounting> existingRecords = this.list(queryWrapper);
            Set<String> existingCodes = existingRecords.stream()
                .map(PositiveMaterialAccounting::getChemicalSystemCode)
                .collect(Collectors.toSet());

            if (!existingCodes.isEmpty()) {
                log.warn("发现与数据库重复的化学体系编号: {}", existingCodes);

                // 为与数据库重复的记录添加错误信息
                for (PositiveMaterialAccountingExcelDto dto : dataList) {
                    String chemicalSystemCode = dto.getChemicalSystemCode();
                    if (StrUtil.isNotBlank(chemicalSystemCode) && existingCodes.contains(chemicalSystemCode)) {
                        String existingError = dto.getErrorMessage();
                        String newError = String.format("化学体系编号 '%s' 在数据库中已存在", chemicalSystemCode);

                        if (StrUtil.isNotBlank(existingError)) {
                            dto.setErrorMessage(existingError + "; " + newError);
                        } else {
                            dto.setErrorMessage(newError);
                        }
                    }
                }
            }
        }

        // 3. 统计验证结果
        long errorCount = dataList.stream()
            .filter(dto -> StrUtil.isNotBlank(dto.getErrorMessage()))
            .count();

        log.info("化学体系编号重复性验证完成，发现错误记录: {} 条", errorCount);
    }

    @Override
    public List<String> checkDuplicateSystemCodes(PositiveMaterialAccountingCheckDuplicateParam param) {
        if (param == null || param.getSystemCodes() == null || param.getSystemCodes().isEmpty()) {
            return new ArrayList<>();
        }

        log.info("开始检查重复正极体系编号，编号数量: {}, 排除ID: {}", param.getSystemCodes().size(), param.getExcludeId());

        // 构建查询条件
        LambdaQueryWrapper<PositiveMaterialAccounting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PositiveMaterialAccounting::getChemicalSystemCode, param.getSystemCodes());

        // 如果有排除ID，则排除该记录
        if (param.getExcludeId() != null) {
            queryWrapper.ne(PositiveMaterialAccounting::getId, param.getExcludeId());
        }

        // 查询重复的记录
        List<PositiveMaterialAccounting> duplicateRecords = this.list(queryWrapper);

        // 提取重复的正极体系编号
        List<String> duplicateSystemCodes = duplicateRecords.stream()
            .map(PositiveMaterialAccounting::getChemicalSystemCode)
            .distinct()
            .collect(java.util.stream.Collectors.toList());

        log.info("检查重复正极体系编号完成，发现重复编号: {}", duplicateSystemCodes);
        return duplicateSystemCodes;
    }
}