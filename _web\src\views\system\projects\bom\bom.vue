<template>
    <div class="setup-bom-content" :style="{paddingBottom : bom.bomStatus == 3 ? '35px' : '0'}" >
        <div v-show="tipShow" :style="position" id="sgtip" class="moveTip">{{tipContent}}</div>
        <bomaddwerk :werklines="werklines" ref="bomaddwerk" @ok="handleChangeWerk" />

        <a-modal title="提示" :width="400" :visible="saveVisible"  @cancel="no">
            <div :style="{textAlign:'center'}">
                当前系统样品阶段是{{'product_state_status' |dictType(projectdetail.productState)}},请确认是否修改样品阶段为{{'product_state_status' |dictType(bom.productState)}}
            </div>
            <template slot="footer">
                <a-button size='small' class="product_btn product_btn_primary"  @click="yes">确认</a-button>
                <a-button size='small' class="product_btn"  @click="no">返回编辑</a-button>
            </template>
        </a-modal>

        <div class="btns">
            <a-button size='small' v-if="!openRightMain && (bom.bomStatus == 0 || bom.bomStatus == 4) " class="product_btn product_btn_primary"
                @click="openRight" icon="menu-fold">
                选择物料
            </a-button>
            <a-button size='small' v-else-if="bom.bomStatus == 0 || bom.bomStatus == 4" class="product_btn" @click="openRight"
                icon="menu-unfold">
                取消选择
            </a-button>
            <a-button size='small' v-if="bom.id" class="product_btn product_btn_primary" @click="preview" icon="play-circle">
                预览
            </a-button>
            <!-- <a-button size="small" v-if="!bom.id && bomType != 1" class="product_btn product_btn_primary" @click="getDesignBomlist">引用设计平台BOM</a-button> -->
            <a-button v-if="bom.bomStatus == 0 || bom.bomStatus == 4 || bom.bomStatus == 7" size='small' class="product_btn product_btn_primary"  @click="saveBom()"
                icon="save">保存</a-button>
            <a-button size='small' class="product_btn product_btn_primary"  @click="close()"
                icon="minus-circle">关闭</a-button>
            <a-modal :width="360" :visible="copyModal" title="复制bom" @ok="handleOk"
                :confirm-loading="loading" @cancel="no">
                <div class="project_item" :style="{justifyContent:'center'}">
                    <span>复制来源：</span>
                    <a-select v-model="sourceId" placeholder="请选择复制来源"
                        :style="{width: '200px',textAlign: 'center'}">
                        <a-select-option v-for="(item,index) in copySource" :key="index" :value="item.id">
                            {{item.name }}
                        </a-select-option>
                    </a-select>
                </div>
            </a-modal>
            <a-drawer placement="right" :closable="false" :bodyStyle="{ height: '100%' }" width="80%"
                :visible="showPdf" @close="closePdf">
                <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%"></iframe>
            </a-drawer>
            <bomupgrade ref="bomupgrade" @updateVis="updateVis" />
        </div>

        <strong>一、基本信息</strong>
        <div class="project_details" ref="projectDetail">

            <div class="project_item">
                <span class="label">产品名称:</span>
                <span>{{ projectdetail.productProjectName }}</span>
            </div>

            <div class="project_item">
                <span class="label">
                    <a-tooltip title="样品阶段为必填选项">
                        <span class="required-mark">*</span>样品阶段:
                    </a-tooltip>
                </span>
                <a-select v-model="bom.productState" :getPopupContainer="(triggerNode) =>triggerNode.parentNode" placeholder="请选择样品阶段" :disabled="disabled" @change="(value) => {
                        if(value){
                            if(bom.id){
                                sysBomSave(false,false)
                            }
                        }
                    }">
                    <a-select-option disabled  value="3" v-if="parseInt(bom.productState) == 3 && projectdetail.productState > 3" >B样</a-select-option>
                    <template v-for="(item,i) in getDict('product_state_status')">
                        <a-select-option  :key="i" :value="item.code" v-if="parseInt(item.code) >= bom.lastProductState && parseInt(item.code) <= projectdetail.productState" >{{item.name}}</a-select-option>
                    </template>
                </a-select>
            </div>

            <div class="project_item">
                <span class="label">
                    <a-tooltip title="项目阶段为必填选项">
                        <span class="required-mark">*</span>项目阶段:
                    </a-tooltip>
                </span>
                <a-select v-model="bom.productStageId" :getPopupContainer="(triggerNode) =>triggerNode.parentNode" placeholder="请选择项目阶段" :disabled="disabled" @change="(value) => {
                        if(value){
                            bom.productStage = this.getDictName('product_stage_status',value+'')
                            if(bom.id){
                                sysBomSave(false,false)
                            }
                        }
                    }">
                    <template v-if="projectdetail.productState > 3">
                        <a-select-option v-for="(item,i) in getDict('product_stage_status')"  :key="i" :value="item.code" v-if="parseInt(item.code) > 8">{{item.name}}</a-select-option>
                    </template>
                    <template v-else-if="projectdetail.productState == 3">
                        <a-select-option v-for="(item,i) in getDict('product_stage_status')"  :key="i" :value="item.code" v-if="parseInt(item.code) > 5 && parseInt(item.code) < 10">{{item.name}}</a-select-option>
                    </template>
                </a-select>
                
            </div>
            
            <div class="project_item">
                <span class="label" v-if="bom.bomType == 1">
                    <a-tooltip title="运输方式为必填选项">
                        <span class="required-mark">*</span>运输方式:
                    </a-tooltip>
                </span>
                <span v-else class="label">运输方式:</span>
                <a-select :getPopupContainer="(triggerNode) =>triggerNode.parentNode" placeholder='请选择运输方式' :showArrow="true" mode="multiple" :maxTagCount="1" :disabled="disabled" :value="bomTransport" @change="(value) => {
                        let oldvalue = bomTransport
                        if(value.length > 0){
                            bomTransport= value
                            if(bom.id){
                                sysBomSave(false,false)
                            }
                        }else if(value.length < 1){
                            bomTransport= []
                            if(bom.id){
                                sysBomSave(false,false)
                            }
                        }
                    }">
                    <a-select-option value='1'>陆运</a-select-option>
                    <a-select-option value='2'>海运</a-select-option>
                    <a-select-option value='3'>空运</a-select-option>
                    <a-select-option value='4'>其他</a-select-option>
                </a-select>
            </div>

            <div class="project_item" v-if="bomTransport.find(b => '4' == b) != null">
                <span class="label" v-if="bom.bomType == 1">
                    <a-tooltip title="其他运输方式为必填选项">
                        <span class="required-mark">*</span>其他运输方式:
                    </a-tooltip>
                </span>
                <span v-else class="label">其他运输方式:</span>
              <a-input placeholder='请输入其他运输方式' :disabled="disabled" v-model="bom.bomTransportOther" @change="(value) => {if(value){
                            if(bom.id){
                                sysBomSave(false,false)
                            }
                        }
                    }"></a-input>
            </div>

            <div class="project_item">
                <span class="label">
                    <a-tooltip title="适用工厂为必填选项">
                        <span class="required-mark">*</span>适用工厂:
                    </a-tooltip>
                </span>
                <span v-if="(bom.bomStatus == '0' || bom.bomStatus == '4' || bom.bomStatus == '7') && !bom.isCheck" class="project_field" :style="{cursor:'pointer'}" @click="addWerks(bom)">
                    <div :style="{display:'flex',alignItems: 'center',justifyContent: 'space-between'}" v-if="bom.lines && bom.lines.length > 0">
                        <div>
                            <template v-for="(item,i) in bom.lines"  >
                                {{dataLines[item]}}<span :key="i" style="margin: 0 1px;">;</span>
                            </template>
                        </div>
                        <a-icon :style="{color:'#ccc',float:'right',marginTop:'3px'}" type="down" />
                    </div>
                    <span :style="{color:'#bfbfbf'}" v-else>设置工厂<a-icon :style="{color:'#ccc',float:'right',marginTop:'3px',marginRight:'12px'}" type="down" /></span>
                </span>
                <span v-else :style="{color: '#333', backgroundColor: '#f5f5f5'}"  class="project_field" >
                    <div :style="{display:'flex',alignItems: 'center',justifyContent: 'space-between'}" v-if="bom.lines && bom.lines.length > 0">
                        <div>
                            <template v-for="(item, i) in bom.lines" >
                                {{dataLines[item]}}<span :key="i" style="margin: 0 1px;">;</span>
                            </template>
                        </div>
                        <a-icon :style="{color:'#ccc',float:'right',marginTop:'3px'}" type="down" />
                    </div>
                    <span :style="{color:'#333'}" v-else>禁止操作<a-icon :style="{color:'#ccc',float:'right',marginTop:'3px',marginRight:'12px'}" type="down" /></span>
                </span>
            </div>

            <div class="project_item">
                <span class="label">有效起始日:</span>
                <a-date-picker :disabled="disabled" 
                    @change="(date, dateString) => {
                        bom.bomStartdate = dateString
                        if(bom.id){
                            sysBomSave(false,false)
                        }
                    }" 
                    v-model="bom.bomStartdate"
                    :default-value="moment(new Date(),'YYYY-MM-DD')" 
                />
            </div>
        </div>

        <a-card v-if="bom.bomStatus == 3" :style="{marginBottom:'8px',height: clientHeight+'px',overflowY: 'scroll'}">
            <p class="error">{{errorTips}}</p>
            <div v-for="(item, n) in add_fails" :key="n">
                <p class="error" v-for="(_item,i) in item.IT_DATA" :key="i">
                    新增失败: 主物料-{{_item.MATNR}} 子物料-{{_item.IDNRK}} 用量-{{_item.MENGE}} 单位-{{_item.MEINS}}
                    损耗率-{{_item.AUSCH}}
                </p>
            </div>
            <div v-for="(item, n) in edit_fails" :key="n">
                <p class="error" v-for="(_item,i) in item.IT_DATA" :key="i">
                    {{fldelte[_item.FLDELETE]}}失败: 主物料-{{item.IV_MATNR}} 子物料-{{_item.IDNRK}} 用量-{{_item.MENGE}}
                    损耗率-{{_item.AUSCH}}
                </p>
            </div>
        </a-card>

        <strong>二、BOM搭建</strong>
        <div class="main" >
            <div class="left_main" :style="{height: bom.bomStatus == 3 ? 'auto' : clientHeight +'px', width: openRightMain ? '45%' : '100%'}">  
                
                    <dragTreeTable id="dratree" ref="dratree" :style="{width: !openRightMain ? 'auto':'auto'}" :isdraggable="isdraggable" :data='treeData' resize >
                        
                        <template #desc="{row}">
                            <div @click.stop class="stopdiv borderdiv">
                                <input :disabled="disabled" @change="(e) => {
                                        const { value } = e.target
                                        row.desc = value
                                        sysBomSave(false,false)
                                    }" v-model="row.desc" placeholder="备注" />
                            </div>
                        </template>

                        <template #partUse="{row}">
                            <input class="readonly text-align" :value="row.partUse" readonly="readonly" />
                        </template>

                        <template #partLoss="{row}">
                            <div @click.stop class="stopdiv borderdiv">
                                <input v-if="row.parent_id" type="number" :disabled="disabled"
                                    :value="row.partLoss" @change="(e) => {
                                        const { value } = e.target
                                        if(!value){
                                            $message.error('请输入数值')
                                            row.partLoss = 0
                                        }
                                        if(!equal(row.partLoss,parseFloat(value).toFixed(2))){
                                            let val = value
                                            if(val > 100){
                                                val = 100
                                            }
                                            row.partLoss = val
                                        }
                                    }" min="0" step="0.01" precision="2" max="100.00" /><span
                                    v-if="row.parent_id" style="margin-left: 8px;">%</span>
                            </div>
                        </template>
                        <template #sapNumber="{row}">
                            <div class="stopdiv" @click.stop>
                                <input class="readonly text-align" :value="row.sapNumber" readonly="readonly" />
                            </div>
                        </template>
                        <template #partDescription="{row}">
                            <div class="stopdiv" @click.stop>
                                <input class="readonly" :value="row.partDescription" readonly="readonly" />
                            </div>
                        </template>
                        <template #sapPartUse="{row}">
                            <div class="stopdiv" @click.stop>
                                <input class="readonly text-align" :value="row.sapPartUse" readonly="readonly" />
                            </div>
                        </template>
                        <template #baseUse="{row}">
                            <div class="stopdiv" @click.stop>
                                <input v-if="row.parent_id" class="readonly text-align" :value="row.baseUse"
                                    readonly="readonly" />
                            </div>
                        </template>
                        <template #count="{row}">
                            <div v-if="row.substitute && row.substitute.length > 0" @click.stop class="stopdiv">
                                <a @click="$refs.bomreplaceForm.add(row,partGroupArr)">{{row.substitute.length}}</a>
                            </div>
                            <div v-else @click.stop class="stopdiv">
                                <a @click="$refs.bomreplaceForm.add(row,partGroupArr)">0</a>
                            </div>
                        </template>

                        <template #version="{row}">
                            <div @click.stop class="stopdiv">
                                <template v-if="row.version">
                                    <div v-for="(item,i) in JSON.parse(row.version)" :key="i">
                                        {{i}}-{{item}}
                                    </div>
                                </template>
                            </div>
                        </template>

                        <template #partUnit="{row}">
                            <input class="readonly" :value="row.partUnit" readonly="readonly" />
                        </template>

                        <template #sapImport="{row}">
                            <div @click.stop>
                                <a class="product_a"
                                    v-if="!disabled && (row.sapNumber.startsWith('8') || row.sapNumber.startsWith('9'))"
                                    @click="sapImport(row)">
                                    导入sap
                                </a>
                                <a v-else-if="!disabled" :style="{display:'inline-block',width:'12px',marginRight:'16px'}"></a>
                            </div>
                        </template>
                    </dragTreeTable>
                
                <span class="arrow_btn left" :style="{height: clientHeight+'px'}" @click="openHide" v-if="showAll && (bom.bomStatus == 0 || bom.bomStatus == 4) "></span>
                <span class="arrow_btn right" :style="{height: clientHeight+'px'}" @click="openShow" v-else-if="bom.bomStatus == 0 || bom.bomStatus == 4"></span>
            </div>

            
            <div :class="{ right_main_show: !openRightMain }" class="right_main">
                <strong>BOM搭建</strong><span style="color: #999;font-size: 12px;font-weight: 400;">（可点击左侧半成品物料分类进行搭建）</span>
                <div class="steps">
                    <div :class="{ active: !tonext }" class="seq">1</div>
                    <div>半成品搭建</div>
                    <div class="line"></div>
                    <div :class="{ active: tonext }" class="seq">2</div>
                    <div>半成品子物料搭建
                        <span v-if="optRow">({{optRow.sapNumber}})</span>
                    </div>
                </div>
                <div v-show="!tonext" >
                    <a-spin class="step_spin" :spinning="loading">
                        <DragLevelsTable :onDrag="onTreeDataChange" :beforeDragOver="beforeDragOver" id="dratree1" ref="dratree1" :style="{width:'auto'}"  :isdraggable="true" :data='initTreeData' >
                            <template #sapNumber="{row}">
                                <div class="stopdiv" @click.stop>
                                    <div v-if="!row.parent_id && bom.isCheck">
                                        <input class="readonly text-align" :value="row.sapNumber" readonly="readonly" />
                                    </div>
                                    <a-auto-complete
                                            v-else
                                            option-label-prop="value"
                                            :allowClear="true"
                                            @change="onChange($event,row)"
                                            @select="onSelect($event, row)"
                                            @search="onAutoSearch"
                                            :dropdown-match-select-width="false"
                                            :dropdown-style="{ width: '180px'}"
                                            v-model="row.sapNumber"
                                        >
                                        <template slot="dataSource">
                                            <a-select-option v-for="item in dataSource" :key="item.id" :value="item.sapNumber">
                                                {{item.sapNumber}}-{{item.partName}}
                                            </a-select-option>
                                        </template>
                                        <a-input size='small' :style="{textAlign:'left'}">
                                            <a-icon slot="suffix" type="search" />
                                        </a-input>
                                    </a-auto-complete>
                                </div>
                            </template>
                            
                            <template #partVerify="{row}">
                                <input v-if="row.partUnit" :style="{color:'green'}" class="readonly text-align" value="Y" readonly="readonly" />
                                
                                <input  v-else-if="row.sapNumber" :style="{color:'red'}" class="readonly text-align" value="N" readonly="readonly" />
                                
                                <span v-else></span>
                            </template>

                            <template #partUse="{row}">
                                <div class="stopdiv numberdiv" @click.stop>
                                    <template v-if="!row.parent_id">
                                        <span>{{row.partUse}}</span>
                                        <!-- <input type="number" class="readonly text-align" :value="row.partUse" readonly="readonly" /> -->
                                    </template>
                                    <template v-else>
                                        <input :id="row.id" type="number" 
                                            @change="(e) => {
                                                const { value } = e.target
                                                row.validate = validatePrimeNumber(value)
                                                if(!row.validate){
                                                    return
                                                }
                                                if(!equal(row.partUse,parseFloat(value).toFixed(3))){
                                                    row.partUse = value
                                                }
                                            }" min="0" step="0.001" v-model="row.partUse" 
                                        />
                                    </template>
                                </div>
                            </template>

                            <template #partUnit="{row}">
                                <div class="stopdiv numberdiv" @click.stop v-if="!row.parent_id">
                                    <span>{{row.partUnit}}</span>
                                    <!-- <input class="readonly text-align" :value="row.partUnit" readonly="readonly" /> -->
                                </div>
                                <div @click.stop class="stopdiv numberdiv" v-else>
                                    <input  @change="(e) => {
                                            const { value } = e.target
                                            row.partUnit = value
                                            //sysBomSave(false,false)
                                        }" v-model="row.partUnit" placeholder="单位" />
                                </div>
                            </template>

                            <template #partDescription="{row}">
                                <div class="stopdiv" @click.stop>
                                    <a-popover placement="bottom">
                                        <template slot="content">
                                            <div >{{row.partDescription}}</div>
                                        </template>
                                        <input :style="{textAlign:'center'}" class="text_ellipsis readonly text-align" :value="row.partDescription" readonly="readonly" />
                                    </a-popover>
                                </div>
                            </template>

                           <!--  <template #partUnit="{row}">
                                <div class="stopdiv" @click.stop>
                                    <input class="readonly text-align" :value="row.partUnit" readonly="readonly" />
                                </div>
                            </template> -->

                            <template #action="{row}">
                                <div @click.stop>
                                    <a-popconfirm placement="topRight" :title="!bom.isCheck ? '请确认是否新增半成品？' : '新增半成品有升版风险，请联系管理员进行确认'"
                                        @confirm="() => addOne(row)">
                                        <a class="product_a">新增</a>
                                    </a-popconfirm>
                                    
                                    <a-popconfirm v-if="row.parent_id" placement="topRight" :title="!bom.isCheck ? `删除物料分类${nodeMap.get(row.partClass)? nodeMap.get(row.partClass) : row.partClass}，会将下层所有的物料删除，请确认是否删除？`: `删除物料分类${nodeMap.get(row.partClass)? nodeMap.get(row.partClass) : row.partClass}，会将下层所有的物料删除，此次操作会有升版风险，请联系管理员确认`"
                                        @confirm="() => doDel(row)">
                                        <a class="product_a" style="margin-left:8px">删除</a>
                                    </a-popconfirm>
                                </div>
                            </template>

                        </DragLevelsTable>

                    </a-spin>
                    <div class="bom_btns" >
                        <a-button class="product_btn product_btn_primary" size='small'
                            @click="next">
                            下一步
                        </a-button>
                        <a-button class="product_btn" size='small'
                            @click="openRight">
                            取消
                        </a-button>
                    </div>
                </div>
                <div v-show="tonext" :style="{paddingTop:'2px',marginTop:'8px'}">
                    
                    <a-tabs size="small" :activeKey="activeKey" type="card" @change="handleTabsChange" destroyInactiveTabPane>
                        <a-tab-pane v-for="item in tabs" :key="item.key" :tab="item.tab">  
                        </a-tab-pane>
                    </a-tabs>

                    <div class="form" :style="{marginTop:'12px'}">
                        <div class="form_row">
                            <div class="form_item" :style="{flex:'2'}">
                                <label :style="{fontSize:'12px',color:'#333'}">物料代码：</label>
                                <a-input-search @keyup.enter.native="getParts" :style="{fontSize:'12px',width:'185px'}" v-model="queryParam.sapNumber" placeholder="多个物料以英文空格隔开" />
                                <div class="form_tip">
                                    <span class="icon svg-icon-wrap svg-ze svg-ze-ze-question-o-wrap" style="margin-top: 4px;"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-gbp7ch-0 bRiIWI svg-icon-path-icon fill" viewBox="0 0 32 32" width="16" height="16"><defs data-reactroot=""></defs><g><path d="M16 28.444c-6.86 0-12.444-5.584-12.444-12.444s5.584-12.444 12.444-12.444 12.444 5.584 12.444 12.444-5.584 12.444-12.444 12.444zM16 1.778c-7.854 0-14.222 6.368-14.222 14.222s6.368 14.222 14.222 14.222c7.854 0 14.222-6.368 14.222-14.222s-6.368-14.222-14.222-14.222zM16.207 8.889c-1.428 0-2.56 0.409-3.38 1.25-0.841 0.821-1.25 1.934-1.25 3.362v0.032c0 0.574 0.462 1.038 1.036 1.038 0.572 0 1.036-0.464 1.036-1.038 0-0.012-0.004-0.032-0.004-0.032 0-0.862 0.174-1.543 0.526-2.012 0.391-0.548 1.035-0.821 1.916-0.821 0.702 0 1.25 0.196 1.639 0.587 0.372 0.391 0.569 0.919 0.569 1.602 0 0.508-0.178 0.976-0.53 1.426l-0.332 0.372c-1.209 1.074-1.931 1.888-2.228 2.421-0.443 0.798-0.363 1.536-0.363 1.536 0 0.572 0.466 1.036 1.038 1.036 0.574 0 0.953-0.395 1.036-1.036 0 0 0.107-0.597 0.322-0.988 0.176-0.35 0.45-0.684 0.8-0.996 0.94-0.802 1.486-1.31 1.66-1.525 0.469-0.624 0.722-1.426 0.722-2.382 0-1.172-0.389-2.11-1.15-2.795-0.782-0.702-1.797-1.035-3.067-1.035zM15.873 20.318c-0.409 0-0.743 0.117-0.996 0.391-0.295 0.252-0.432 0.587-0.432 0.996 0 0.391 0.137 0.724 0.432 0.997 0.252 0.272 0.587 0.409 0.996 0.409 0.393 0 0.743-0.137 1.036-0.389 0.272-0.276 0.409-0.606 0.409-1.017 0-0.409-0.137-0.743-0.409-0.996-0.274-0.274-0.626-0.391-1.036-0.391z" fill="#999"></path></g></svg></span>
                                    <div class="tooltiptext">
                                        <p>支持多行复制</p>
                                    </div>
                                </div>
                            </div>
                            <div class="form_item" :style="{justifyContent:'right'}">
                                <a-button size='small' class="product_btn product_btn_primary" @click="getParts" >查询</a-button>
                            </div>
                        </div>
                    </div>
                    <a-spin class="step_spin part_table" :spinning="loading">
                        <a-table :style="{width:'100%'}" ref="table" :pagination="false" :rowKey="(record) => record.id" :columns="partColumns"   :dataSource="partsData">
                            <span slot="id" slot-scope="text, record">
                                <span v-if="record.base == 1"></span>
                                <span v-else-if="optIds.indexOf(text) != -1 && optSelectIds.indexOf(text) != -1">
                                    <a-icon @click="toggleSelect(record)" :style="{color:'#1c983b'}" type="check-circle"  theme="filled" />
                                </span>
                                <span v-else-if="optSelectIds.indexOf(text) != -1">
                                    <a-icon @click="toggleSelect(record)" :style="{color:'blue'}" type="check-circle"  theme="filled"/>
                                </span>
                                <span v-else-if="optSelectIds.indexOf(text) == -1">
                                    <a-icon @click="toggleSelect(record)" :style="{color:'grey'}" type="check-circle"  theme="filled"/>
                                </span>

                            </span>
                             <!-- <template #partUnit="{row}">
                                <div class="stopdiv numberdiv" @click.stop v-if="!row.parent_id">
                                    <input class="readonly text-align" :value="row.partUnit" readonly="readonly" />
                                </div>
                                <div @click.stop class="stopdiv numberdiv" v-else>
                                    <input  @change="(e) => {
                                            const { value } = e.target
                                            row.partUnit = value
                                            //sysBomSave(false,false)
                                        }" v-model="row.partUnit" placeholder="单位" />
                                </div>
                            </template> -->
                            <span slot="partUnit" slot-scope="text, record">
                                <template v-if="record.base == 1">
                                    {{text}}
                                </template>
                                <template v-else>
                                    <div class="numberdiv">
                                         <input :id="record.id"  :style="{width:'70px'}"
                                            @change="(e) => {
                                                const { value } = e.target
                                                record.partUnit = value
                                            }" min="0" step="0.001" v-model="record.partUnit" 
                                        />
                                    </div>
                                   
                                </template>
                            </span>
                            <span slot="partUse" slot-scope="text, record">
                                <template v-if="record.base == 1">
                                    {{text}}
                                </template>
                                <template v-else>
                                    <div class="numberdiv">
                                         <input :id="record.id" type="number" :style="{width:'70px'}"
                                            @change="(e) => {
                                                const { value } = e.target
                                                record.validate = validatePrimeNumber(value)
                                                if(!record.validate){
                                                    return
                                                }
                                                if(!equal(record.partUse,parseFloat(value).toFixed(3))){
                                                    record.partUse = value
                                                }
                                            }" min="0" step="0.001" v-model="record.partUse" 
                                        />
                                    </div>
                                   
                                </template>
                            </span>
                            
                            <span slot="partVerify" slot-scope="text, record">
                                <span :style="{color:'green'}" v-if="record.partUnit">Y</span>
                                <span :style="{color:'red'}" v-else-if="record.sapNumber">N</span>
                                <span v-else></span>
                            </span>
                            <span slot="partName" slot-scope="text">
                                <a-popover placement="bottom">
                                    <template slot="content">
                                        <div>{{text}}</div>
                                    </template>
                                    <div class="text_ellipsis">{{text}}</div>
                                </a-popover>
                            </span>
                            <span slot="partDescription" slot-scope="text">
                                <a-popover placement="bottom">
                                    <template slot="content">
                                        <div>{{text}}</div>
                                    </template>
                                    <div class="text_ellipsis">{{text}}</div>
                                </a-popover>
                            </span>
                            <span slot="action" slot-scope="text, record">
                                <a-popconfirm v-if="record.base != 1" placement="topRight" title="确认删除？"
                                    @confirm="() => delOpt(record)">
                                    <a class="product_a">删除</a>
                                </a-popconfirm>
                            </span>
                        </a-table>
                    </a-spin>
                    <div class="bom_btns" :style="{marginTop:'8px'}">
                        <a-button class="product_btn product_btn_primary" size='small'
                            @click="prev">
                            上一步
                        </a-button>
                        <a-popconfirm placement="topLeft" :title="`${optRow ? nodeMap.get(optRow.partClass)+optRow.sapNumber : ''}的子物料搭建完毕，请再次确认填写的物料料号及用量单位是否准确。`"
                            @confirm="() => addParts()">
                            <a-button class="product_btn product_btn_primary" size='small'>确认</a-button>
                        </a-popconfirm>
                        
                        <a-button class="product_btn" size='small'
                            @click="openRight">
                            取消
                        </a-button>
                    </div>
                    
                </div>
                
            </div>
        </div>
        <designbom ref='designbom' @ok="designBom" />
        <sapimport ref="sapimport" />
        <bomreplace @substitute="substitute" :nodeMap="nodeMap" :nodes="nodes" :disabled="disabled" ref="bomreplaceForm" />
    </div>
</template>

<script>
    import designbom from './designbom';
    import { mapGetters } from 'vuex'
    import {
        DICT_TYPE_TREE_DATA
    } from '@/store/mutation-types'
    import Vue from 'vue'
    import {
        getPartList,
        getParts,
        getPartRandom
    } from "@/api/modular/system/partManage"
    import {
        sysBomSave,
        getBom,
        pdfUpdate,
        getBomError,
        getBomList,
        copyBom,
        sapImportVerify,
        updatePLMBomData,
        sysBomAdd,
        getwerklines,
        initBaseBom,
        getDesignBomlist
    } from "@/api/modular/system/bomManage"
    import {
        getAllNode
    } from "@/api/modular/system/nodeManage"

    import dragTreeTable from "drag-tree-table";
    import {
        STable,
        DragLevelsTable
    } from '@/components'
    import bomreplace from './bomreplace'
    import moment from 'moment';
    import Iframe from "../../../../layouts/Iframe";
    import bomupgrade from './bomupgrade'
    import sapimport from './sapimport'
    import bomaddwerk from './addwerk'
    export default {
        components: {
            Iframe,
            dragTreeTable,
            STable,
            bomreplace,
            bomupgrade,
            sapimport,
            bomaddwerk,
            DragLevelsTable,
            designbom
        },
        props: {
            bomType:{
                type: Number,
                default: 0
            },
            issueId: {
                type: Number,
                default: 0
            },
            bomId: {
                type: String,
                default: 0
            },
            projectdetail: {
                type: Object,
                default: {}
            },
        },
        data() {
            return {
                timeId:null,
                tabs:[],
                activeKey:'',
                clientHeight: 0,
                tableWidthObj:[
                    {
                        field:'id',
                        title: '物料分类',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.15,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.15,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.24,

                    },
                    {
                        field: 'sapNumber',
                        title: '物料代码',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.08,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.08,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.08,

                    },
                    {
                        field: 'partDescription',
                        title: '物料规格',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.26,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.26,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.32,

                    },
                    {
                        field: 'partUnit',
                        title: '单位',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.04,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.04,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.04,
                    },
                    {
                        field: 'partUse',
                        title: '理论用量(B0)',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.1,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.1,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.1,
                    },
                    {
                        field: 'partLoss',
                        title: '工艺损耗(B1)%',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.11,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.11,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.17,
                    },
                    {
                        field: 'desc',
                        title: '备注',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.08,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.08,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.08,
                        
                    },
                    {
                        field: 'version',
                        title: '工厂版本号',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.06,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.09,

                    },
                    {
                        field: 'sapPartUse',
                        title: 'sap使用量',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.06,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.09,

                    },
                    {
                        field: 'sapImport',
                        title: 'sap导入',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34) * 0.06,
                    },
                ],

                tableWidthObj0or4:[
                    {
                        field:'id',
                        title: '物料分类',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.15,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.15,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.24,

                    },
                    {
                        field: 'sapNumber',
                        title: '物料代码',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.08,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.08,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.08,

                    },
                    {
                        field: 'partDescription',
                        title: '物料规格',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.26,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.26,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.32,

                    },
                    {
                        field: 'partUnit',
                        title: '单位',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.04,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.04,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.04,
                    },
                    {
                        field: 'partUse',
                        title: '理论用量(B0)',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.1,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.1,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.1,
                    },
                    {
                        field: 'partLoss',
                        title: '工艺损耗(B1)%',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.11,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.11,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.17,
                    },
                    {
                        field: 'desc',
                        title: '备注',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.08,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.08,
                        noSapWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.08,
                        
                    },
                    {
                        field: 'version',
                        title: '工厂版本号',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.06,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.09,

                    },
                    {
                        field: 'sapPartUse',
                        title: 'sap使用量',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.06,
                        noSapImportWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.09,

                    },
                    {
                        field: 'sapImport',
                        title: 'sap导入',
                        showAllWidth:(document.body.clientWidth - 40 - 20 - 34 - 18) * 0.06,
                    },
                ],
                
                is0or4:false,
                showAll:true,
                werklines: {},
                dataLines: {},
                saveVisible:false,
                delIds:[],
                tonext:false,
                dataSource:[],
                position: {
                    left: '',
                    top: ''
                },
                tipContent: '', 
                tipShow: false,
                sourceId: null,
                copyModal: false,
                optRow: null,
                optIds: [],
                optSelectIds: [],
                disabled: false,
                isdraggable: false,
                nodes: [],
                bom: {
                    isCheck: false,
                    productState: this.projectdetail.productState + '',
                    lastProductState: this.projectdetail.productState == 3 ? '3' : '4',
                    bomStartdate:moment(new Date(),'YYYY-MM-DD'),
                    bomStatus: 0,
                    productStageId: this.projectdetail.productStage,
                    productStage: this.projectdetail.productStageName
                }, 
                nodeMap: new Map(),
                openRightMain: false,
                visible: false,
                showPdf: false,
                queryParam: {},
                loading: false,
                pdfUrl: '',
                initTreeData: {
                    open: true,
                    namess: "",
                    lists: [],
                    columns: [
                        {
                            type: 'selection',
                            field: 'id',
                            title: '物料分类',
                            align: 'center',
                            width: document.body.clientWidth*0.5*0.4,
                            formatter: (item) => {
                                let that = this
                                return `<input style="margin-left:4px; display: inline-block;color:#1890ff; width:auto;max-width: 80%;outline:none" data-val="${that.nodeMap.get(item.partClass)? that.nodeMap.get(item.partClass) : item.partClass}" data-id="${item.id}" value="${that.nodeMap.get(item.partClass)? that.nodeMap.get(item.partClass) : item.partClass}" class="text_ellipsis readonly1 text-align initinput" />`
                            }
                        },
                        {
                            type: 'sapNumber',
                            field: 'sapNumber',
                            width: document.body.clientWidth*0.5*0.27,
                            title: '物料代码',
                            align: 'center'
                        },
                        {   
                            type: 'partDescription',
                            field: 'partDescription',
                            title: '物料规格',
                            width: document.body.clientWidth*0.5*0.4,
                            align: 'center'
                        },
                        {
                            field: 'partUnit',
                            type: 'partUnit',
                            title: '单位',
                            width: document.body.clientWidth*0.5*0.05,
                            align: 'center'
                        },
                        {
                            type: 'partUse',
                            field: 'partUse',
                            width: document.body.clientWidth*0.5*0.1,
                            title: '理论用量',
                            align: 'center'
                        },
                        {
                            type: 'partVerify',
                            field: 'partVerify',
                            align: 'center',
                            width: document.body.clientWidth*0.5*0.2,
                            title: '物料校验',
                        },
                        {
                            type: 'action',
                            field: 'action',
                            align: 'left',
                            width: document.body.clientWidth*0.5*0.2,
                            title: '操作',
                        },
                    ]
                },
                partColumns:[
                    {
                        dataIndex: 'id',
                        width: 40,
                        align: "center",
                        title: '选择',
                        scopedSlots: {
                            customRender: 'id'
                        },
                    },
                    {
                        title: '物料名称',
                        dataIndex: 'partName',
                        scopedSlots: {
                            customRender: 'partName'
                        },
                        width: 90,
                        align: "center"
                    },
                    {
                        title: '物料代码',
                        dataIndex: 'sapNumber',
                        width: 80,
                        align: "center",
                    },
                    {
                        title: '物料规格',
                        ellipsis: true,
                        width: 80,
                        dataIndex: 'partDescription',
                        scopedSlots: {
                            customRender: 'partDescription'
                        },
                        align: "center"
                    },
                    {
                        title: '单位',
                        dataIndex: 'partUnit',
                        scopedSlots: {
                            customRender: 'partUnit'
                        },
                        width: 60,
                        align: "center"
                    },
                    {
                        title: '理论用量(B0)',
                        dataIndex: 'partUse',
                        align: "center",
                        scopedSlots: {
                            customRender: 'partUse'
                        },
                        width: 100,
                    },
                    {
                        title: '物料校验',
                        dataIndex: 'partVerify',
                        align: "center",
                        width: 60,
                        scopedSlots: {
                            customRender: 'partVerify'
                        },
                    },
                    {
                        title: '操作',
                        dataIndex: 'action',
                        align: "center",
                        width: 50,
                        scopedSlots: {
                            customRender: 'action'
                        }
                    },
                ],
                partsData:[
                ],
                treeData: {
                    open: true,
                    namess: "",
                    lists: [],
                    columns: [{
                        type: 'selection',
                        field: 'id',
                        title: '物料分类',
                        align: 'center',
                        formatter: (item) => {
                            let that = this
                            return `<a onclick="optPart()" title="${item.partName}" class="aid product_a" data-sapNumber=${item.sapNumber} data-id=${item.id} name=${that.nodeMap.get(item.partClass)}>${that.nodeMap.get(item.partClass)}</a>`
                        }
                    },
                    {
                        type: 'sapNumber',
                        field: 'sapNumber',
                        title: '物料代码',
                        align: 'center'
                    },
                    {
                        type: 'partDescription',
                        field: 'partDescription',
                        title: '物料规格',
                        align: 'center'
                    },
                    {
                        type: 'partUnit',
                        field: 'partUnit',
                        title: '单位',
                        align: 'center'
                    },
                    {
                        type: 'partUse',
                        field: 'partUse',
                        title: '理论用量(B0)',
                        align: 'center'
                    },
                    {
                        type: 'partLoss',
                        field: 'partLoss',
                        title: `
                        <div class='partLoss'>工艺损耗(B1)%
                            <span class="icon svg-icon-wrap svg-ze svg-ze-ze-question-o-wrap" style="margin-top: 2px;"><svg xmlns="http://www.w3.org/2000/svg" class="styles__StyledSVGIconPathComponent-sc-gbp7ch-0 bRiIWI svg-icon-path-icon fill" viewBox="0 0 32 32" width="12" height="12"><defs data-reactroot=""></defs><g><path d="M16 28.444c-6.86 0-12.444-5.584-12.444-12.444s5.584-12.444 12.444-12.444 12.444 5.584 12.444 12.444-5.584 12.444-12.444 12.444zM16 1.778c-7.854 0-14.222 6.368-14.222 14.222s6.368 14.222 14.222 14.222c7.854 0 14.222-6.368 14.222-14.222s-6.368-14.222-14.222-14.222zM16.207 8.889c-1.428 0-2.56 0.409-3.38 1.25-0.841 0.821-1.25 1.934-1.25 3.362v0.032c0 0.574 0.462 1.038 1.036 1.038 0.572 0 1.036-0.464 1.036-1.038 0-0.012-0.004-0.032-0.004-0.032 0-0.862 0.174-1.543 0.526-2.012 0.391-0.548 1.035-0.821 1.916-0.821 0.702 0 1.25 0.196 1.639 0.587 0.372 0.391 0.569 0.919 0.569 1.602 0 0.508-0.178 0.976-0.53 1.426l-0.332 0.372c-1.209 1.074-1.931 1.888-2.228 2.421-0.443 0.798-0.363 1.536-0.363 1.536 0 0.572 0.466 1.036 1.038 1.036 0.574 0 0.953-0.395 1.036-1.036 0 0 0.107-0.597 0.322-0.988 0.176-0.35 0.45-0.684 0.8-0.996 0.94-0.802 1.486-1.31 1.66-1.525 0.469-0.624 0.722-1.426 0.722-2.382 0-1.172-0.389-2.11-1.15-2.795-0.782-0.702-1.797-1.035-3.067-1.035zM15.873 20.318c-0.409 0-0.743 0.117-0.996 0.391-0.295 0.252-0.432 0.587-0.432 0.996 0 0.391 0.137 0.724 0.432 0.997 0.252 0.272 0.587 0.409 0.996 0.409 0.393 0 0.743-0.137 1.036-0.389 0.272-0.276 0.409-0.606 0.409-1.017 0-0.409-0.137-0.743-0.409-0.996-0.274-0.274-0.626-0.391-1.036-0.391z"></path></g></svg></span>
                            <div class="tooltiptext">
                                <div>研发只输出设计用量，不输出工艺损耗，此工艺损耗仅限于删除工艺损耗变更时使用</div>
                            </div>
                        </div>`,
                        align: 'center'

                    },
                    {
                        type: 'desc',
                        field: 'desc',
                        title: '备注',
                        align: 'center'
                    },
                    {
                        type: 'version',
                        field: 'version',
                        title: '工厂版本号',
                    },
                    {
                        type: 'sapPartUse',
                        field: 'sapPartUse',
                        title: 'sap使用量',
                    },
                    ]
                },
                overColumns:[
                    {
                        type: 'version',
                        field: 'version',
                        title: '工厂版本号',
                        align: 'center'

                    },
                    {
                        type: 'sapPartUse',
                        field: 'sapPartUse',
                        title: 'sap使用量',
                        align: 'center'

                    },
                ],
                bomTransport: [],
                partGroupArr: [],
                add_fails: [],
                edit_fails: [],
                errorTips: '',
                fldelte: {
                    'M': '修改',
                    'A': '新增',
                    'X': '删除'
                },
                copySource: [],
                initData:[
                ],
            };
        },
        methods: {
            designBom(params){
                let that = this
                let bomData = JSON.parse(params.bomData)
                that.initDesignBom(bomData)
                bomData[0].validate = true
                
                that.treeData.lists = bomData
                that.sysBomAdd(false)
                setTimeout(() => {
                    that.getlevelid()
                    that.tabs = that.getInitialTabs(that.treeData.lists)
                    that.getBaseBom()
                    this.handleTableWidth()
                }, 1000);
            },
            initDesignBom(list){
                let that = this
                let arr = []
                for (const item of list) {
                   
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()

                        

                        _tempItem.partUse = _tempItem.partUse ? Math.round( parseFloat(_tempItem.partUse) * 1000) / 1000 : 0
                        _tempItem.sapPartUse = _tempItem.sapPartUse ? Math.round( parseFloat(_tempItem.sapPartUse) * 1000) / 1000 : 0
                        _tempItem.baseUse = _tempItem.baseUse ? Math.round( parseFloat(_tempItem.baseUse) * 1000) / 1000 : 0
                        _tempItem.validate = (_tempItem.sapNumber &&  _tempItem.partUse &&  _tempItem.partUnit ) ? true : false


                        if (_tempItem.substitute && _tempItem.substitute.length > 0) {
                            that.partGroupArr.push(_tempItem.partGroup)
                        }
                        if (_tempItem.sapNumber != null && _tempItem.sapNumber.startsWith('8')) {
                            
                           _tempItem.base = 1
                           _tempItem.open = true
                        }
                        if (_tempItem.lists) {
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
            },
            initBaseBom(){
                let that = this
                initBaseBom({bomType:that.bomType}).then((res) => {
                    if (res.success) {
                        that.partUse2Null(res.data)
                        that.initTreeData.lists = res.data
                       setTimeout(() => {
                            that.getinitlevelid()
                        }, 1000);
                    } else {
                        that.$message.error(res.message)
                    }
                }).finally((res) => {
                })
            },
            initTreeOpen(list){
                let arr = []
                for (const item of list) {
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()
                        if (_tempItem.sapNumber != null && _tempItem.sapNumber.startsWith('8')) {
                           _tempItem.open = true
                        }
                        if (_tempItem.lists) {
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
            },
            getInitialTabs(list) {
                let _tab = []
                let arr = []
                for (const item of list) {
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()
                        if (_tempItem.sapNumber.startsWith('8')) {
                            _tab.push({
                                key : _tempItem.id,
                                tab : this.nodeMap.get(_tempItem.partClass)
                            })
                        }
                        if (_tempItem.lists) {
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
                return _tab
            },
            handleTabsChange(key){
                let that = this
                that.activeKey = key
                if (that.optRow && (that.optRow.id+'') != key) {
                    that.queryParam = {}
                }
                that.optRow = that.getOptRow(that.treeData.lists,parseInt(key))
                that.optIds = that.optRow.lists.map(item => item.id)
                that.optSelectIds = JSON.parse(JSON.stringify(that.optIds))
                that.partsData = JSON.parse(JSON.stringify(that.optRow.lists))
                that.hightLow(that.optRow.id)
                that.initTableBody()
            },
            initTableBody() {
                let that = this
                that.$nextTick(() => {
                    let _items = that.getByClass(document,'ant-table-placeholder')
                    //let items = that.getByClass(document,   "ant-table-content")
                    let $items = that.getByClass(document,'ant-table-body')
                    for (const e of items) {
                        e.style.height = _items[0] ? (this.clientHeight - 150)+'px' : 'auto'
                    }
                    /* for (const e of $items) {
                        e.style.height = _items[0] ? 'auto' : (this.clientHeight - 150)+'px'
                    } */
                })
            },
            openHide(){
                this.showAll = false
                this.treeData.columns.splice(7,this.overColumns.length)
            },
            openShow(){
                this.showAll = true
                this.treeData.columns.splice(7,0,...this.overColumns)
            },
            addWerks(bom){
                if (!this.bom.id) {
                    this.$message.error('请搭建BOM')
                    return
                }
                this.$refs.bomaddwerk.edit(bom)
            },
            getwerklines() {
                getwerklines({werkType: 1}).then((res) => {
                    if (res.success) {
                        let mapline = {}
                        for (var key in res.data) {
                            for (const _item of res.data[key]) {
                                mapline[_item.id] =// _item.namecode && _item.namecode.length > 0 ? _item.namecode + (_item.lineName && _item.lineName.length > 0 ? '-' + _item.lineName : '') :
                                _item.werkNo + ( _item.lineName && _item.lineName.length > 0 ? '->' + _item.lineName : '')
                            }
                        }
                        this.dataLines = mapline
                        this.werklines = res.data;
                    } else {
                        this.$message.error(res.message)
                    }
                }).finally((res) => {
                })
            },
            yes(){
                this.saveVisible = false
                this.$message.info('已保存')
            },
            no(){
                this.saveVisible = false
                this.copyModal = false
            },
            saveBom(){
                if (!this.bom.id) {
                    this.$message.error('请搭建BOM')
                    return
                }
                if (this.projectdetail.productState != this.bom.productState) {
                    this.saveVisible = true
                }else{
                    this.$message.info('已保存')
                }
            },
            getDictName(code,key) {
                const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
                let dict = dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
                let name = dict.find(item=>item.code == key)?.name
                return name ?? '-'
            },
            getDict(code) {
                const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
                return dictTypeTree.filter(item => item.code == code)[0].children
            },
            delOpt(record){

                let that = this
                if (record.id && that.optIds.indexOf(record.id) != -1) {
                    that.delIds.push(record.id)
                }
                if (record.id) {
                    let i = that.partsData.findIndex(item => item.id == record.id)
                    that.partsData.splice(i,1)
                }else{
                    let i = that.partsData.findIndex(item => item.sapNumber == record.sapNumber)
                    that.partsData.splice(i,1)
                }

                that.initTableBody()
            },
            close(){
                this.$emit('closeBom')
            },
            addParts(){
                let that = this
                let selectParts = that.partsData.filter(item => that.optSelectIds.indexOf(item.id) != -1)
                if (!that.optRow) {
                    that.$message.error('请选择父级物料')
                    return
                }

                for (const item of selectParts) {
                    if (!that.validatePrimeNumber(item.partUse)) {
                        that.$message.error('存在理论用量为0的物料,操作失败')
                        return 
                    }
                }

                if (selectParts.length > 0) {
                    let map = {}
                    let ids = []
                    that.toMap(that.treeData.lists, map)
                    that.getParentIds(map, selectParts[0].parent_id, ids)
                    for (const item of selectParts) {
                        if (ids.indexOf(item.sapNumber) != -1) {
                            that.$message.error('存在与父物料相同的子物料,操作失败')
                            return 
                        }
                    }
                }
                let selectIds = selectParts.map(item => item.id)
                for (const item of that.optRow.lists) {
                    if (selectIds.indexOf(item.id) != -1) {
                        let _item = selectParts.find(e=>e.id == item.id)
                        item.partUse = _item.partUse
                        item.partUnit = _item.partUnit
                    }
                }

                that.optRow.lists.push(
                    ...JSON.parse(JSON.stringify(selectParts.filter(item => that.optIds.indexOf(item.id) == -1)))
                )

                that.optIds.push(
                    ...selectParts.filter(item => that.optIds.indexOf(item.id) == -1).map(item=>item.id)
                )

                that.optRow.lists = that.optRow.lists.filter(item => that.delIds.indexOf(item.id) == -1)

                that.sysBomSave(true,true)
                setTimeout(() => {
                    that.getlevelid()
                    that.tabs = that.getInitialTabs(that.treeData.lists)
                }, 1000); 

                
            },
            toggleSelect(record){
                if (!record.id) {
                    this.$message.error('该物料库中不存在')
                    return false
                }
                let index = this.optSelectIds.indexOf(record.id)
                if (index != -1) {
                    this.optSelectIds.splice(index,1)
                }else{
                    this.optSelectIds.push(record.id)
                }
            },
            getParts(){
                let that = this
                if (!that.queryParam.sapNumber) {
                    that.$message.error('请输入物料代码')
                    return false
                }
                if (!that.optRow) {
                    that.$message.error('请选择父级物料')
                    return false
                }
                let sapNumbers = that.queryParam.sapNumber.split(' ').filter(item=>item.length > 0)
                let b = new Set(sapNumbers).size !== sapNumbers.length
                if (b) {
                    that.$confirm({
                        title: '输入内容含有相同的物料，是否添加？',
                        onOk() {
                            that.callGetParts({sapNumbers:sapNumbers});
                            that.queryParam.sapNumber = ''
                        },
                        onCancel() { },
                        class: 'test',
                    });
                    return false
                }
                let i = that.partsData.findIndex(item => sapNumbers.indexOf(item.sapNumber+'') != -1 ) 
                if (i != -1) {
                    that.$confirm({
                        title: '存在相同的物料，是否添加？',
                        onOk() {
                            that.callGetParts({sapNumbers:sapNumbers});
                            that.queryParam.sapNumber = ''
                        },
                        onCancel() { },
                        class: 'test',
                    });
                    return false
                }
                
                that.callGetParts({sapNumbers:sapNumbers});
                that.queryParam.sapNumber = ''
            
            },
            callGetParts(params){
                let that = this
                let _data = []
                getParts(params).then((res) => {
                    if (res.success) {
                        let _sapNumbers = params.sapNumbers.filter((value, index, self) => {
                            return self.indexOf(value) === index;
                        });
                        for (const item of _sapNumbers) {
                            let _item = res.data.filter(d=>d.sapNumber == item)
                            if (_item.length > 0) {
                                for (const e of _item) {
                                    _data.push({
                                        'id': e.id,
                                        'open': true,
                                        'base': 0,
                                        'partName': e.partName,
                                        'partDescription': e.partDescription,
                                        'partUnit': e.partUnit,
                                        'partClass': e.partClass,
                                        'sapNumber': e.sapNumber ,
                                        'partUse': null,
                                        'sapPartUse': null,
                                        'baseUse': null,
                                        'partLoss': 0,
                                        'partNumber':  e.partNumber,
                                        'parent_id': this.optRow.id,
                                        'partGroup': '',
                                        'posnr': '',
                                        'desc': '',
                                        'version': '',
                                        'substitute': [],
                                        'validate': false,
                                        'lists': []
                                    })
                                }
                            }else{
                                _data.push({
                                    'sapNumber':  item,
                                })
                            }
                        }
                        that.partsData.push(..._data)
                        that.optSelectIds.push(..._data.filter(item=>item.id != null).map(item=>item.id))
                    }
                })
            },
            prev(){
                this.queryParam = {}
                this.tonext = false
            },
            next(){
                let arr = []
                let _arr = []
                let that = this
                let isYes = true
                let level2Sapnumber = []
                
                let _copyTree = JSON.parse(JSON.stringify(that.initTreeData.lists))
                
                for (let item of that.initTreeData.lists) {
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()
                        if (!_tempItem.level) {
                            _tempItem.level = 1;
                        }
                        if (!_tempItem.sapNumber || !_tempItem.validate || !_tempItem.partUnit) {
                            if (isYes) {
                                isYes = false
                            }
                        }else{
                            level2Sapnumber.push({
                                level:_tempItem.level,
                                sapNumber : _tempItem.sapNumber
                            })
                            _arr.push(_tempItem)
                        }
                        if (_tempItem.lists) {
                            for (let _item of _tempItem.lists) {
                                _item.level = _tempItem.level + 1
                            }
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
                if (!isYes) {
                    that.$message.error('请检查物料代码以及理论用量')
                    return
                }
                const newArr = level2Sapnumber.map(item => item.sapNumber);
	            const isRepeat = newArr.some((x, index, arr) => level2Sapnumber.findIndex(y => y.sapNumber == x) != index);
                if (isRepeat) {
                    that.$message.error('半成品存在相同的物料')
                    return
                }

                if (!that.bom.id) {
                    that.treeData.lists = JSON.parse(JSON.stringify(that.initTreeData.lists))
                    that.sysBomAdd(true)
                    setTimeout(() => {
                        that.getlevelid()
                    }, 1000);
                    return
                }

                let $arr = []
                let $_arr = []
                for (const item of that.treeData.lists) {
                    $arr.push(item)
                    while ($arr.length > 0) {
                        let _tempItem = $arr.shift()

                        if (_tempItem.base == 1) {
                            $_arr.push(_tempItem)
                        }

                        if (_tempItem.lists) {
                            $arr.push(..._tempItem.lists)
                        }
                    }
                }

                for (let item of _arr) {
                    let _item = $_arr.find(e=>e.id == item.id)
                    if (_item) {
                        item.lists.push(..._item.lists.filter(e=>e.base!=1))
                        if (that.bom.isCheck && item.sapNumber != _item.sapNumber) {
                            item.id = item.anoterId
                            for (const e of item.lists) {
                                e.parent_id = item.anoterId
                            }
                            delete item.anoterId
                        }
                    }
                }

                that.treeData.lists = JSON.parse(JSON.stringify(that.initTreeData.lists))

                that.initTreeData.lists = _copyTree

                that.bomSave(true,true)
                setTimeout(() => {
                    this.getlevelid()
                }, 1000);

            },
            onChange(val,record){
                let _item = this.dataSource.find(item => item.sapNumber == val)
                if (!val || !_item) {
                    record.partDescription = ''
                    record.partClass = ''
                    record.partNumber = ''
                    record.partUnit = ''
                    record.partGroup = ''
                    record.posnr = ''
                    record.desc = ''
                    record.version = ''
                    if (!val) {
                        record.sapNumber = ''
                    }
                }
            },
            onSelect(val,record){
                let that = this
                let _item = this.dataSource.find(item => item.sapNumber == val)
                if (_item) {
                    if (that.bom.isCheck) {
                        let _row = that.getOptRow(that.treeData.lists,record.id)
                        if (_row && _row.sapNumber != _item.sapNumber) {
                            that.$message.error('此次操作有升版风险，请谨慎操作或恢复为原来的物料')
                            getPartRandom({}).then((res) => {
                                if (res.success) {
                                    record.anoterId = res.data
                                    record.base = 1
                                    record.sapNumber = _item.sapNumber
                                    record.partDescription = _item.partDescription
                                    record.partClass = _item.partClass
                                    record.partNumber = _item.partNumber
                                    record.partUnit = _item.partUnit
                                    record.partName = _item.partName
                                    record.partGroup = ''
                                    record.posnr = ''
                                    record.desc = ''
                                    record.version = ''
                                    setTimeout(() => {
                                        that.getinitlevelid()
                                    }, 1000);
                                }else{
                                    this.$message.error('网络出错,请重试')
                                }
                                return
                            })
                            return
                        }
                    }
                    record.base = 1
                    record.sapNumber = _item.sapNumber
                    record.partDescription = _item.partDescription
                    record.partClass = _item.partClass
                    record.partNumber = _item.partNumber
                    record.partUnit = _item.partUnit
                    record.partName = _item.partName
                    record.partGroup = ''
                    record.posnr = ''
                    record.desc = ''
                    record.version = ''
                    setTimeout(() => {
                        that.getinitlevelid()
                    }, 1000);
                }

            },
            onAutoSearch(val){
                getPartList({partNumber:val,pageNo:1,pageSize:80,flag:0}).then((res) => {
                    this.dataSource = res.data.rows
                })
            },
            mouseDown() {
                let that = this
                // 鼠标跟随tip
                document.onmousedown = function (e) {
                    var ev = e || event;
                    var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
                    that.position.left = ev.clientX + 'px';
                    that.position.top = ev.clientY - 50 + scrollTop + 'px';
                }
                document.onmouseup = function (e) {
                    that.tipShow = false
                    that.tipContent = ''
                }
            },
            mouseUp() {
                let that = this
                document.onmouseup = function (e) {
                    that.tipShow = false
                    that.tipContent = ''
                }
            },
            sapImport(row) {
                let that = this
                sapImportVerify({
                    id: that.bom.id
                }).then((res) => {
                    if (res.success) {
                        that.$refs.sapimport.add(that.bom.id, row)
                    } else {
                        that.$message.error(res.message)
                    }
                })
            },
            copy() {
                this.getBomList();
            },
            getDesignBomlist(){
                let that = this
                getDesignBomlist({
                    account: this.userInfo.account,
                    productName: this.projectdetail.productProjectName
                }).then((res)=>{

                    this.$refs.designbom.view(res.data);
                })
            },
            getBomList() {
                let that = this
                that.copySource = []
                getBomList({
                    bomIssueId: that.issueId,
                    bomType: that.bomType
                }).then((res) => {
                    if (res.success) {
                        for (let i = 0; i < res.data.length; i++) {
                            if (that.bom.id != res.data[i].id && res.data[i].bomData.length > 2) {
                                res.data[i].name = (i + 1) + '、' + (res.data[i].bomNo ? res.data[i].bomNo : JSON.parse(res.data[i].bomData)[0].partDescription)
                                that.copySource.push(res.data[i]);
                            }
                        }
                    }
                }).finally((res) => {
                    that.copyModal = true
                })
            },
            getByClass(parent, cls) {
                if (parent.getElementsByClassName) {
                    return Array.from(parent.getElementsByClassName(cls));
                } else {
                    var res = [];
                    var reg = new RegExp(' ' + cls + ' ', 'i')
                    var ele = parent.getElementsByTagName('*');
                    for (var i = 0; i < ele.length; i++) {
                        if (reg.test(' ' + ele[i].className + ' ')) {
                            res.push(ele[i]);
                        }
                    }
                    return res;
                }
            },
            validatePrimeNumber(number) {
                if (this.equal(number, 0.000)) {
                    return false
                }
                return true;
            },
            closePdf() {
                this.showPdf = false;
            },
            preview() {
                let that = this
                if (that.bom.bomData.length < 3) {
                    that.$message.error("请先搭建bom");
                    return false
                }
                that.loading = true
                pdfUpdate({
                    id: that.bom.id
                }).then((res) => {
                    if (res.success) {
                        that.pdfUrl = '/api/sysFileInfo/preview?Authorization=Bearer '+Vue.ls.get('Access-Token')+'&id=' + res.data
                        that.showPdf = true
                        that.loading = false
                    }
                })
            },
            partUse2Null(list) {
                let arr = []
                for (const item of list) {
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()
                        if (_tempItem.partUse <= 0) {
                            _tempItem.partUse = null
                            _tempItem.sapPartUse = null
                        }
                        if (_tempItem.lists) {
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
            },
            toMap(list, _map) {
                let arr = []
                for (const item of list) {
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()
                        _map[_tempItem.id] = {
                            sapNumber: _tempItem.sapNumber,
                            parent_id: _tempItem.parent_id
                        }
                        if (_tempItem.lists) {
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
            },
            getParentIds(_map, id, ids) {
                let $map = null
                if (_map[id]) {
                    $map = _map[id]
                    while ($map) {
                        let _tempItem = $map
                        $map = null
                        ids.push(_tempItem.sapNumber)
                        if (_tempItem.parent_id) {
                            $map = _map[_tempItem.parent_id]
                        }
                    }
                }
            },
            moment,
            equal(a, b) {
                const floatEpsilon = Math.pow(2, -23)
                return Math.abs(a - b) <= floatEpsilon * Math.max(Math.abs(a), Math.abs(b));
            },
            openRight() {
                this.openRightMain = !this.openRightMain
                
                if (this.openRightMain && !this.tonext && this.bom.id) {
                    this.getBaseBom()
                }
                if (!this.openRightMain) {
                    this.optRow = null
                    this.optIds = []
                    this.optSelectIds = []
                    this.partsData = []
                    this.queryParam = {}
                    this.tonext = false
                }
            },
            handleTableWidth(){
                let target =  this.showAll ? ( this.treeData.columns.length < 10 ? 'noSapImportWidth' : 'showAllWidth') : 'noSapWidth'
                let is0or4 = this.bom.bomStatus == '0' || this.bom.bomStatus == '4' || (this.bom.bomStatus == '7' && this.hasPerm('sysBom:copy'))
                this.treeData.columns.forEach(v => {
                    v.width =  (is0or4 ? this.tableWidthObj0or4 : this.tableWidthObj).filter(e => e.field == v.field)[0][target] //- (is0or4 ? 15:0)
                })
            },
            beforeDragOver(from, to, where) {
                let that = this
                clearTimeout(that.timeId)
                let fromName = that.nodeMap.get(from.partClass)? that.nodeMap.get(from.partClass) : from.partClass
                let toName = that.nodeMap.get(to.partClass)? that.nodeMap.get(to.partClass) : to.partClass
                that.tipContent = fromName + '将移动到' + toName + (!that.bom.isCheck ? '':',此次操作有升版风险，请联系管理员进行确认')
                that.tipShow = true
                that.timeId =setTimeout(() => {
                    that.tipContent = ''
                    that.tipShow = false
                }, 1800);
            },
            onTreeDataChange(list, from, to, where) {
                let that = this
                if (that.bom.bomStatus == 7) {
                    that.$message.error('导入状态不允许拖拽')
                    return false
                }
                if (!from.parent_id && that.initTreeData.lists.length > 0) {
                    that.$message.error('顶级BOM已存在')
                    return false;
                }
                that.initTreeData.lists = list;
                setTimeout(() => {
                    that.getinitlevelid()
                }, 1000);
            },

            getlevelid() {
                let that = this
                that.$nextTick(() => {
                    let items = that.getByClass(document, 'aid')
                    for (const e of items) {
                        e.innerText = '[' + (that.$refs.dratree.GetLevelById(e.getAttribute('data-id')) + 1) + ']' + '-' + e.getAttribute('name')
                    }
                })
            },
            getinitlevelid() {
                let that = this
                that.$nextTick(() => {
                    let items = that.getByClass(document, 'initinput')
                    for (const e of items) {
                        let val = e.getAttribute('data-val')
                        e.value = '[' + (that.$refs.dratree1.GetLevelById(e.getAttribute('data-id')) + 1) + ']' + '-' + val
                    }
                })
            },
            hightLow(id) {
                let that = this
                that.$nextTick(() => {
                    let items = that.getByClass(document, 'tree-row')
                    for (const e of items) {
                        let $id = e.getAttribute('tree-id')
                        if ($id == id) {
                            e.classList.add('highlight')
                        } else {
                            e.classList.remove('highlight')
                        }
                    }
                })
            },
            numDiv(num1, num2) {
                num1 = num1 ? num1 : 0.000
                num2 = num2 ? num2 : 0.000
                var baseNum1 = 0,
                    baseNum2 = 0;
                var baseNum3, baseNum4;
                try {
                    baseNum1 = num1.toString().split(".")[1].length;
                } catch (e) {
                    baseNum1 = 0;
                }
                try {
                    baseNum2 = num2.toString().split(".")[1].length;
                } catch (e) {
                    baseNum2 = 0;
                }
                baseNum3 = Number(num1.toString().replace(".", ""));
                baseNum4 = Number(num2.toString().replace(".", ""));
                return (baseNum3 / baseNum4) * Math.pow(10, baseNum2 - baseNum1);
            },
            getSapPartUse() {
                this.loopSaveBomSap(this.treeData.lists)
            },
            loopSaveBomSap(list) {
                let that = this
                let arr = []
                for (const item of list) {
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()
                        if (_tempItem.lists) {
                            for (const _item of _tempItem.lists) {
                                let a = _item.partUse
                                let b = that.numDiv(_tempItem.partUse, 1000)
                                _item.sapPartUse = that.numDiv(a, b)//.toFixed(3)
                                if (!new RegExp("(^$)|^[0-9]+(.?[0-9]{1,3})?$").test(_item.sapPartUse)) {
                                    _item.sapPartUse = _item.sapPartUse.toFixed(3)
                                }
                                _item.baseUse = parseFloat(a * (1 + that.numDiv(_item.partLoss, 100)))//.toFixed(3)
                                if (!new RegExp("(^$)|^[0-9]+(.?[0-9]{1,3})?$").test(_item.baseUse)) {
                                    _item.baseUse = _item.baseUse.toFixed(3)
                                }
                            }
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
            },
            onSearch(query) {
                this.search = query
                this.offset = 0
            },
            
            sysBomAdd(next){
                let that = this
                that.loading = true
                that.getSapPartUse()
                that.bom.bomTransport = JSON.stringify(that.bomTransport)
                if (typeof that.bom.bomStartdate != 'string') {
                    that.bom.bomStartdate = moment(that.bom.bomStartdate).format('YYYY-MM-DD')
                }
                let _bom = {...that.bom,...{bomData: JSON.stringify(that.treeData.lists),bomIssueId: that.projectdetail.issueId,bomType: that.bomType}}
                sysBomAdd(_bom)
                .then((res) => {
                    if (res.success) {
                        res.data.productState = res.data.productState + ''
                        res.data.lastProductState = res.data.lastProductState + ''
                        that.bom = res.data
                        if (next) {
                            that.tabs = that.getInitialTabs(that.treeData.lists)
                            that.tonext = next
                            that.optRow = that.treeData.lists[0]
                            that.optIds = that.optRow.lists.map(item => item.id)
                            that.optSelectIds = JSON.parse(JSON.stringify(that.optIds))
                            that.partsData = JSON.parse(JSON.stringify(that.optRow.lists))
                            that.hightLow(that.optRow.id)
                            that.activeKey = that.optRow.id + ''
                        }
                    } else {
                        this.$message.error(res.message, 1);
                    }
                }).finally(() => {
                    that.loading = false
                });
            },
            bomUpGrade() {
                this.$refs.bomupgrade.edit(this.bom)
            },
            updateVis() {
                this.disabled = true;
            },
            addOne(row){
                let that = this
                getPartRandom({}).then((res) => {
                    if (res.success) {
                        row.lists.push({
                            'id': res.data,
                            'open': true,
                            'partName': null,
                            'partDescription': null,
                            'partUnit':null,
                            'partClass': '',
                            'sapNumber': null,
                            'partUse': null,
                            'sapPartUse': null,
                            'baseUse': null,
                            'partLoss': 0,
                            'partNumber': null,
                            'parent_id': row.id,
                            'partGroup': '',
                            'posnr': '',
                            'desc': '',
                            'version': '',
                            'substitute': [],
                            'validate': false,
                            'lists': []
                        })
                        setTimeout(() => {
                            that.getinitlevelid()
                        }, 1000);
                    }
                })
                
            },
            doDel(row) {
                if (!row.parent_id) {
                    this.$message.info('顶级BOM不可删除', 1);
                    return
                }
                let that = this
                that.removeTreeListItem(this.initTreeData.lists, row.id, that)
                that.initTableBody()
            },
            removeTreeListItem(treeList, id, that) {
                if (!treeList || !treeList.length) {
                    return
                }
                for (let i = 0; i < treeList.length; i++) {
                    if (treeList[i].id === id) {
                        treeList.splice(i, 1);
                        return false;
                    }
                    that.removeTreeListItem(treeList[i].lists, id, that)
                }
            },

            resetOptRow(list) {
                let that = this
                let arr = []
                for (const item of list) {
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()
                        if (_tempItem.id == that.optRow.id ) {
                            that.optRow = _tempItem
                            that.hightLow(that.optRow.id)
                            that.activeKey = that.optRow.id + ''
                            return false
                        }
                        if (_tempItem.lists) {
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
            },
            bomSave(onSapUse,next) {
                let that = this
                if (!next) {
                    that.loading = true
                }
                if (onSapUse) {
                    that.getSapPartUse()
                }
                
                that.bom.bomTransport = JSON.stringify(that.bomTransport)
                that.bom.bomData = JSON.stringify(that.treeData.lists)
                sysBomSave(that.bom)
                    .then((res) => {
                        if (res.success) {
                            that.bom.id = res.data
                            if (next) {
                                that.tonext = next
                                that.tabs = that.getInitialTabs(that.treeData.lists)
                                if (!that.optRow) {
                                    that.optRow = that.treeData.lists[0]
                                    that.optIds = that.optRow.lists.map(item => item.id)
                                    that.optSelectIds = JSON.parse(JSON.stringify(that.optIds))
                                    that.partsData = JSON.parse(JSON.stringify(that.optRow.lists))
                                    that.hightLow(that.optRow.id)
                                    that.activeKey = that.optRow.id + ''
                                }else{
                                    that.resetOptRow(that.treeData.lists)
                                }
                            }
                        } else {
                            that.$message.error(res.message, 1);
                        }
                    }).finally(() => {
                        that.loading = false
                    })
            },
            sysBomSave(onSapUse,next) {
                let that = this
                if (!next) {
                    that.loading = true
                }
                if (onSapUse) {
                    that.getSapPartUse()
                }
                
                that.bom.bomTransport = JSON.stringify(that.bomTransport)
                that.bom.bomData = JSON.stringify(that.treeData.lists)
                sysBomSave(that.bom)
                    .then((res) => {
                        if (res.success) {
                            that.bom.id = res.data
                            if (next) {
                                that.tonext = next
                                that.tabs = that.getInitialTabs(that.treeData.lists)
                                if (!that.optRow) {
                                    that.optRow = that.treeData.lists[0]
                                    that.optIds = that.optRow.lists.map(item => item.id)
                                    that.optSelectIds = JSON.parse(JSON.stringify(that.optIds))
                                    that.partsData = JSON.parse(JSON.stringify(that.optRow.lists))
                                    that.hightLow(that.optRow.id)
                                    that.activeKey = that.optRow.id + ''
                                }else{
                                    that.resetOptRow(that.treeData.lists)
                                }
                                
                                that.$message.success((that.optRow ? that.nodeMap.get(that.optRow.partClass)+that.optRow.sapNumber : '')+'的子物料搭建成功',1)
                            }
                        } else {
                            that.$message.error(res.message, 5);
                        }
                    }).finally(() => {
                        that.loading = false
                    })
            },
            getPartGoup(list) {
                let that = this
                let arr = []
                for (const item of list) {
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()
                        if (_tempItem.substitute && _tempItem.substitute.length > 0) {
                            that.partGroupArr.push(_tempItem.partGroup)
                        }
                        if (_tempItem.lists) {
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
            },
            callGetBomError() {
                let that = this
                that.loading = true
                getBomError({
                    id: that.bom.id
                }).then((res) => {
                    if (res.success) {
                        that.add_fails = JSON.parse(res.data.addFails)
                        that.edit_fails = JSON.parse(res.data.editFails)
                        that.errorTips = res.data.errorMsg
                    } else {
                        that.$message.error(res.message, 1);
                    }
                }).finally(() => {
                    that.loading = false
                })
            },
            getBom() {
                let that = this
                that.openRightMain = false
                that.loading = true
                getBom({
                    id: that.bomId ? that.bomId : that.bom.id
                }).then((res) => {
                        if (res.success) {
                            let is0or4 = res.data.bomStatus == '0' || res.data.bomStatus == '4' || (res.data.bomStatus == '7' && that.hasPerm('sysBom:copy'))
                            that.isdraggable = false
                            that.disabled = is0or4 ? false : true
                            that.openRightMain = is0or4 ? true : false
                            that.bom = res.data
                            that.bom.productState = res.data.productState + ''
                            that.bom.lastProductState = res.data.lastProductState == 0 ? (this.projectdetail.productState == 3 ? 3 : 4 ): res.data.lastProductState
                            let _list = JSON.parse(that.bom.bomData)
                            _list[0].base = 1
                            _list[0].seq = 1
                            that.initTreeOpen(_list)
                            that.getPartGoup(_list)
                            that.treeData.lists = _list
                            that.bomTransport = that.bom.bomTransport ? JSON.parse(that.bom.bomTransport) : []
                            setTimeout(() => {
                                that.getlevelid()
                                that.tabs = that.getInitialTabs(that.treeData.lists)
                            }, 1000);

                            if (that.bom.bomStatus == 3) {
                                that.callGetBomError()
                            }
                            if (is0or4) {
                                this.getBaseBom()
                            }
                            let index = that.treeData.columns.findIndex(item => item.type == 'sapImport')
                            
                            if (that.hasPerm('sysBom:copy') && index < 0 && that.bom.canImport == 1) {
                                
                                let $index = that.overColumns.findIndex($item => $item.type == 'sapImport')
                                if ($index == -1 && is0or4){
                                    that.overColumns.push({
                                        type: 'sapImport',
                                        field:'sapImport',
                                        align: 'center',
                                        title: 'sap导入'
                                    })
                                    that.treeData.columns.push({
                                        type: 'sapImport',
                                        field:'sapImport',
                                        align: 'center',
                                        title: 'sap导入'
                                    })
                                } 
                                    
                            }
                            this.handleTableWidth()
                            
                        } else {
                            that.$message.error(res.message, 1);
                        }
                    }).finally(() => {
                        that.loading = false
                        // 更新表格的高度，因为工厂可能会把页的高度撑大
                        this.clientHeight = document.documentElement.clientHeight - 200 - this.$refs.projectDetail.clientHeight
                    })
            },
            substitute(partGroup, partGroupAdd, tempgroup) {
                let that = this
                if (partGroup && partGroupAdd && that.partGroupArr.indexOf(partGroup) < 0) {
                    that.partGroupArr.push(partGroup)
                }
                if (!partGroup && !partGroupAdd && tempgroup) {
                    let index = that.partGroupArr.indexOf(tempgroup)
                    that.partGroupArr.splice(index, 1)
                }
                that.sysBomSave(false,false)
            },
            handleOk() {
                let that = this
                if (null == that.sourceId) {
                    that.$message.error("请先选择源头")
                    return
                }
                copyBom({
                    sourceId: that.sourceId,
                    targetId: that.bom.id ? that.bom.id : null
                }).then((res) => {
                    if (res.success) {
                        that.treeData.columns.splice(7,that.overColumns.length)
                        that.bom.id = res.data
                        that.getBom()
                    }
                }).finally(() => {
                    that.copyModal = false
                })
                
            },
            setNodeMap(list, map) {
                let arr = []
                for (const item of list) {
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()
                        map.set(_tempItem.id, _tempItem.name)
                        if (_tempItem.lists) {
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
            },
            getBaseBom() {
                let that = this
                let arr = []
                let list = JSON.parse(JSON.stringify(that.treeData.lists))
                let _tempItem = null

                for (const item of list) {
                    arr.push(item)
                    while (arr.length > 0) {
                        _tempItem = arr.shift()
                        if (_tempItem.lists) {
                            _tempItem.lists = _tempItem.lists.filter(item => item.base == 1)
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
                that.initTreeData.lists = list
                setTimeout(() => {
                    that.getinitlevelid()
                }, 1000);
            },
            optPart(){
                let is0or4 = this.bom.bomStatus == '0' || this.bom.bomStatus == '4' || (this.bom.bomStatus == '7' && this.hasPerm('sysBom:copy'))
                if (!is0or4) {
                    return
                }
                let that = this
                var ev= event ||window.event || arguments.callee.caller.arguments[0]; 
                ev.stopPropagation()
                if (!ev.target.getAttribute('data-sapNumber').startsWith('8')) {
                    that.$message.error('非8物料号不能为BOM')
                    return false;
                }
                if (!that.tonext) {
                    that.tonext = true
                }
                if (!that.openRightMain) {
                    that.openRightMain = true
                }
                if (that.optRow && (that.optRow.id+'') != (ev.target.getAttribute('data-id')+'')) {
                    that.queryParam = {}
                }
                that.optRow = that.getOptRow(that.treeData.lists,ev.target.getAttribute('data-id'))
                that.optIds = that.optRow.lists.map(item => item.id)
                that.optSelectIds = JSON.parse(JSON.stringify(that.optIds))
                that.partsData = JSON.parse(JSON.stringify(that.optRow.lists))
                that.hightLow(that.optRow.id)
                that.activeKey = that.optRow.id + ''
                that.initTableBody()
            },
            getOptRow(list, id) {
                let arr = []
                for (const item of list) {
                    arr.push(item)
                    while (arr.length > 0) {
                        let _tempItem = arr.shift()
                        if (_tempItem.id == id) {
                            return _tempItem
                        }
                        if (_tempItem.lists) {
                            arr.push(..._tempItem.lists)
                        }
                    }
                }
            },
            getAllNode() {
                let that = this
                that.loading = true
                getAllNode({})
                    .then((res) => {
                        if (res.success) {
                            let map = new Map()
                            that.setNodeMap(res.data, map)
                            that.nodeMap = map;
                            that.nodes = res.data
                            if (that.bomId) {
                                that.getBom()
                            }
                        } else {
                            that.$message.error(res.message, 1);
                        }
                        that.loading = false
                    }).finally(() => {
                        that.loading = false
                    })
            },
            // 当适用工厂改变时
            handleChangeWerk(){
                // 更新表格的高度
                this.$nextTick(() => {
                    this.clientHeight = document.documentElement.clientHeight - 200 - this.$refs.projectDetail.clientHeight
                })
                
            }
        },
        watch: {
            tonext:{
                handler(newValue) {
                    if (newValue) {
                        this.initTableBody()
                    }
                },
                immediate: true
            },
            showAll:{
                handler(newValue) {
                    this.handleTableWidth()
                },
            },
            sportChange(){
                this.$nextTick(() => {
                    this.clientHeight = document.documentElement.clientHeight - 200 - this.$refs.projectDetail.clientHeight
                })
            }
        },
        created() {
            this.handleTableWidth()
            if (this.bomId) {
                updatePLMBomData({ id: this.bomId })
            }else{
                this.initBaseBom()
                this.openRightMain = true
            }
            
            this.getwerklines()
            this.getAllNode()
            this.mouseDown()
            this.mouseUp()
            window.optPart = this.optPart       
        },
        mounted() {
            this.clientHeight = document.documentElement.clientHeight - 40 - 16 - 32 - 36 - 16 - this.$refs.projectDetail.clientHeight - 8  - 52
            document.documentElement.style.setProperty(`--height`, `${document.documentElement.clientHeight - 40 - 16 - 32 - 36 - 16 - 16 - 38 - 28 - 40 - 10 - 40}px`)
        },
        computed: {
            ...mapGetters(['userInfo']),
            // 监听运输方式的改变，方便更新表格高度
            sportChange() {
                return this.bomTransport.length;
            },
        },
    }
</script>

<style lang="less" scoped=''>
@import './bom.less';
:root{
    --height:280px;
}
/deep/.ant-table-body{
    max-height: var(--height);
}

</style>