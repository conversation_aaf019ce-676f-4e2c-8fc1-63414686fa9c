package eve.sys.modular.bom.params;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SapEditBomItOrEtParam {

    @JSONField(serialize = false)
    private String mPartName; // 父物料名

    @JSONField(serialize = false)
    private String mPartDescription;// 父物料描述

    @JSONField(serialize = false)
    private String partName; // 物料名

    @JSONField(serialize = false)
    private String partDescription;// 物料描述

    @JSONField(name = "PLMITEMNO")
    private String PLMITEMNO;

    @JSONField(name = "FLDELETE")
    private String FLDELETE; // 更改标识1.M表示修改 2.A表示新增 3.X表示删除
    
    @JSONField(name = "POSNR")
    private String POSNR; // BOM 项目号 （行号 新增时，不需要传输项目号）
    
    @JSONField(name = "IDNRK")
    private String IDNRK; // BOM 组件 （sap料号）修改、删除时BOM项目号与组件必须与SAP一致
    
    @JSONField(name = "MENGE")
    private Double MENGE; // 数量 （3位小数）
    
    @JSONField(name = "POTX2")
    private String POTX2; // BOM 项目文本 （行 2）
    
    @JSONField(name = "AUSCH")
    private Double AUSCH; // 损耗率 （2位小数）
    
    @JSONField(name = "ALPGR")
    private String ALPGR;// 替代项目：组 （事例：01，02，03）
    
    @JSONField(name = "ALPRF")
    private Integer ALPRF; // 替代优先级
    
    @JSONField(name = "EWAHR")
    private String EWAHR; // 替代百分比

    @JSONField(name = "WERKS")
    private String WERKS;//工厂

    @JSONField(name = "MEINS")
    private String MEINS;//组件计量单位

    public SapEditBomItOrEtParam newClone(){        
        SapEditBomItOrEtParam one = new SapEditBomItOrEtParam();
        one.setPLMITEMNO(this.PLMITEMNO);
        one.setFLDELETE(this.FLDELETE);
        one.setPOSNR(this.POSNR);
        one.setIDNRK(this.IDNRK);
        one.setMENGE(this.MENGE);
        one.setAUSCH(this.AUSCH);
        one.setALPGR(this.ALPGR);
        one.setALPRF(this.ALPRF);
        one.setEWAHR(this.EWAHR);
        one.setWERKS(this.WERKS);
        one.setMEINS(this.MEINS);
        return one;
    }


    
}
