package eve.sys.modular.bombill.positivematerialaccounting.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bombill.positivematerialaccounting.dto.PositiveMaterialAccountingExcelDto;
import eve.sys.modular.bombill.positivematerialaccounting.entity.PositiveMaterialAccounting;
import eve.sys.modular.bombill.positivematerialaccounting.param.PositiveMaterialAccountingCheckDuplicateParam;
import eve.sys.modular.bombill.positivematerialaccounting.param.PositiveMaterialImportParam;

import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

public interface IPositiveMaterialAccountingService extends IService<PositiveMaterialAccounting> {
    PageResult<JSONObject> pageList(PositiveMaterialAccounting param);
    List<JSONObject> list(PositiveMaterialAccounting param);

    /**
     * 根据化学体系编号集合批量查询正极材料核算数据
     * @param chemicalSystemCodes 化学体系编号集合
     * @return 正极材料核算数据列表
     */
    List<JSONObject> listByChemicalSystemCodes(Set<String> chemicalSystemCodes);

    Boolean add(PositiveMaterialAccounting param);
    Boolean delete(PositiveMaterialAccounting param);
    Boolean update(PositiveMaterialAccounting param);
    PositiveMaterialAccounting get(PositiveMaterialAccounting param);

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 导入Excel
     *
     * @param file Excel文件
     * @return 导入结果
     */
    List<PositiveMaterialAccountingExcelDto> importExcel(MultipartFile file);

    /**
     * 批量保存导入的数据
     *
     * @param dataList 数据列表
     * @return 保存结果
     */
    Boolean batchSaveImportData(PositiveMaterialImportParam param);

    /**
     * 检查重复的正极体系编号
     *
     * @param param 检查参数
     * @return 重复的正极体系编号列表
     */
    List<String> checkDuplicateSystemCodes(PositiveMaterialAccountingCheckDuplicateParam param);
}