package eve.sys.modular.bombill.bomcostoverview.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import eve.core.context.constant.ConstantContext;
import eve.core.context.constant.ConstantContextHolder;
import eve.core.exception.ServiceException;
import eve.core.pojo.page.PageResult;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.service.ISysBomService;
import eve.sys.modular.bombill.bomaccountingdetail.entity.BomAccountingDetail;
import eve.sys.modular.bombill.bomaccountingdetail.service.IBomAccountingDetailService;
import eve.sys.modular.bombill.bomcostoverview.dto.BomCostOverviewExcelDto;
import eve.sys.modular.bombill.bomcostoverview.entity.BomCostOverview;
import eve.sys.modular.bombill.bomcostoverview.mapper.BomCostOverviewMapper;
import eve.sys.modular.bombill.bomcostoverview.params.BomCostOverviewCopyParam;
import eve.sys.modular.bombill.bomcostoverview.params.BomCostParam;
import eve.sys.modular.bombill.bomcostoverview.params.PositiveType2PartNo;
import eve.sys.modular.bombill.bomcostoverview.param.BomCostCompareParam;
import eve.sys.modular.bombill.bomcostoverview.param.BomCostCompareExportParam;
import eve.sys.modular.bombill.bomcostoverview.result.BomCostCompareResult;
import eve.sys.modular.bombill.bomcostoverview.result.MaterialCostResult;
import eve.sys.modular.bombill.bomcostoverview.service.IBomCostOverviewService;
import eve.sys.modular.bombill.chemicalelementprice.entity.ChemicalElementPrice;
import eve.sys.modular.bombill.chemicalelementprice.service.IChemicalElementPriceService;
import eve.sys.modular.bombill.chemicalelement.entity.ChemicalElement;
import eve.sys.modular.bombill.chemicalelement.service.IChemicalElementService;
import eve.sys.modular.bombill.positivematerialaccounting.service.IPositiveMaterialAccountingService;
import eve.sys.modular.bombill.bomcostoverview.util.BomCostOverviewExcelUtil;
import eve.sys.modular.bombill.positivematerialaccounting.entity.PositiveMaterialAccounting;
import eve.sys.modular.bombill.scenario.entity.Scenario;
import eve.sys.modular.bombill.scenario.service.IScenarioService;
import eve.sys.modular.dict.entity.SysDictData;
import eve.sys.modular.product.param.JiraApiParams;
import eve.sys.modular.product.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * BOM成本总览表Service实现类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
public class BomCostOverviewServiceImpl extends ServiceImpl<BomCostOverviewMapper, BomCostOverview>
        implements IBomCostOverviewService {

    @Resource
    private IBomAccountingDetailService bomAccountingDetailService;

    @Resource
    private IScenarioService scenarioService;

    @Resource
    private IChemicalElementPriceService chemicalElementPriceService;

    @Resource
    private IPositiveMaterialAccountingService positiveMaterialAccountingService;

    @Resource
    private IChemicalElementService chemicalElementService;

    @Override
    public PageResult<BomCostOverview> pageList(BomCostOverview param) {
        log.info("开始根据场景表分组查询BOM成本总览，查询参数: {}", param);

        // 1. 先查询BOM成本总览基础数据
        LambdaQueryWrapper<BomCostOverview> queryWrapper = new LambdaQueryWrapper<>();

        // 核算代码模糊查询
        if (StrUtil.isNotBlank(param.getAccountingCode())) {
            queryWrapper.like(BomCostOverview::getAccountingCode, param.getAccountingCode());
        }

        // 产品名称模糊查询
        if (StrUtil.isNotBlank(param.getProductName())) {
            queryWrapper.like(BomCostOverview::getProductName, param.getProductName());
        }

        // BOM文件编号模糊查询
        if (StrUtil.isNotBlank(param.getBomFileNumber())) {
            queryWrapper.like(BomCostOverview::getBomFileNumber, param.getBomFileNumber());
        }

        // 研究所查询
        if (ObjectUtil.isNotEmpty(param.getDepts())) {
            queryWrapper.in(BomCostOverview::getDept, param.getDepts());
        }

        // 客户类型查询
        if (ObjectUtil.isNotNull(param.getCustomerType())) {
            queryWrapper.eq(BomCostOverview::getCustomerType, param.getCustomerType());
        }

        // 产品状态查询
        if (ObjectUtil.isNotEmpty(param.getProductStatuses())) {
            queryWrapper.in(BomCostOverview::getProductStatus, param.getProductStatuses());
        }

        // 正极体系查询（根据场景表的正极材料核算ID过滤）
        if (ObjectUtil.isNotEmpty(param.getPositiveElectrodeSystems())) {
            // 将化学体系编号转换为正极材料核算ID
            List<Long> positiveMaterialAccountingIds = convertChemicalSystemCodesToIds(param.getPositiveElectrodeSystems());

            if (!positiveMaterialAccountingIds.isEmpty()) {
                // 先从场景表查询包含指定正极材料核算ID的BOM成本总览ID
                LambdaQueryWrapper<Scenario> scenarioWrapper = new LambdaQueryWrapper<>();
                scenarioWrapper.in(Scenario::getPositiveMaterialAccountingId, positiveMaterialAccountingIds);
                scenarioWrapper.select(Scenario::getBomCostOverviewId);

                List<Scenario> scenarios = scenarioService.list(scenarioWrapper);
                if (!scenarios.isEmpty()) {
                    List<Long> bomCostOverviewIds = scenarios.stream()
                            .map(Scenario::getBomCostOverviewId)
                            .distinct()
                            .collect(Collectors.toList());
                    queryWrapper.in(BomCostOverview::getId, bomCostOverviewIds);
                } else {
                    // 如果没有找到匹配的场景，返回空结果
                    queryWrapper.eq(BomCostOverview::getId, -1L);
                }
            } else {
                // 如果没有找到匹配的正极材料核算ID，返回空结果
                queryWrapper.eq(BomCostOverview::getId, -1L);
            }
        }

        // 按创建id倒序排列
        queryWrapper.orderByDesc(BomCostOverview::getId);

        // 2. 执行分页查询
        Page<BomCostOverview> page = new Page<>(param.getPageNo(), param.getPageSize());
        Page<BomCostOverview> pageResult = this.page(page, queryWrapper);

        // 3. 为每个BOM成本总览添加正极体系信息（从场景表获取化学体系编号）
        List<BomCostOverview> records = pageResult.getRecords();

        // 内存排序：先按照id降序，然后按正极体系升序
        records.sort(Comparator
                .comparing(BomCostOverview::getId, Comparator.reverseOrder())
                .thenComparing(BomCostOverview::getPositiveElectrodeSystem, Comparator.nullsLast(Comparator.naturalOrder())));

        log.debug("BOM成本总览记录排序完成，共 {} 条记录", records.size());
        if (!records.isEmpty()) {
            enrichWithPositiveElectrodeSystems(records);
        }

        log.info("BOM成本总览分组查询完成，共查询到{}条记录", records.size());
        return new PageResult<>(pageResult);
    }

    /**
     * 为BOM成本总览记录添加正极体系信息
     * 从场景表获取化学体系编号，并用分号分隔
     */
    private void enrichWithPositiveElectrodeSystems(List<BomCostOverview> records) {
        log.info("开始为{}条BOM成本总览记录添加正极体系信息", records.size());

        // 批量获取所有BOM成本总览ID对应的场景数据
        List<Long> bomCostOverviewIds = records.stream()
                .map(BomCostOverview::getId)
                .collect(Collectors.toList());

        // 查询所有相关的场景数据
        LambdaQueryWrapper<Scenario> scenarioQueryWrapper = new LambdaQueryWrapper<>();
        scenarioQueryWrapper.in(Scenario::getBomCostOverviewId, bomCostOverviewIds);
        scenarioQueryWrapper.isNotNull(Scenario::getPositiveMaterialAccountingId);

        List<Scenario> scenarios = scenarioService.list(scenarioQueryWrapper);
        log.info("查询到{}条场景数据", scenarios.size());

        // 按BOM成本总览ID分组场景数据
        Map<Long, List<Scenario>> scenarioMap = scenarios.stream()
                .collect(Collectors.groupingBy(Scenario::getBomCostOverviewId));

        // 为每个BOM成本总览记录设置正极体系
        for (BomCostOverview record : records) {
            List<Scenario> bomScenarios = scenarioMap.get(record.getId());
            if (bomScenarios != null && !bomScenarios.isEmpty()) {
                // 获取所有不重复的正极材料核算ID，然后查询对应的化学体系编号
                Set<Long> positiveMaterialAccountingIds = bomScenarios.stream()
                        .map(Scenario::getPositiveMaterialAccountingId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                if (!positiveMaterialAccountingIds.isEmpty()) {
                    // 批量查询PositiveMaterialAccounting获取化学体系编号
                    List<PositiveMaterialAccounting> accountingList = positiveMaterialAccountingService.list(
                        Wrappers.<PositiveMaterialAccounting>lambdaQuery()
                            .in(PositiveMaterialAccounting::getId, positiveMaterialAccountingIds)
                            .select(PositiveMaterialAccounting::getChemicalSystemCode)
                    );

                    List<String> chemicalSystemCodes = accountingList.stream()
                            .map(PositiveMaterialAccounting::getChemicalSystemCode)
                            .filter(StrUtil::isNotBlank)
                            .distinct()
                            .collect(Collectors.toList());

                    // 用分号分隔
                    String positiveElectrodeSystem = String.join(";", chemicalSystemCodes);
                    record.setPositiveElectrodeSystem(positiveElectrodeSystem);

                    log.debug("BOM成本总览ID: {}, 正极体系: {}", record.getId(), positiveElectrodeSystem);
                } else {
                    record.setPositiveElectrodeSystem("");
                    log.debug("BOM成本总览ID: {}, 无正极材料核算ID", record.getId());
                }
            } else {
                record.setPositiveElectrodeSystem("");
                log.debug("BOM成本总览ID: {}, 无正极体系数据", record.getId());
            }
        }

        log.info("正极体系信息添加完成");
    }

    @Override
    public PageResult<Scenario> scenarioGroupPageList(BomCostOverview param) {
        log.info("开始根据场景ID分组查询BOM成本总览（SQL层分组分页），查询参数: {}", param);

        // 1. 根据BOM成本总览的查询条件，先查询符合条件的BOM成本总览ID
        List<Long> bomCostOverviewIds = getBomCostOverviewIdsByConditions(param);

        if (bomCostOverviewIds.isEmpty()) {
            log.warn("没有找到符合条件的BOM成本总览记录");

            /*
             * // 如果没有查询条件，使用所有BOM成本总览ID进行测试
             * if (isEmptyQueryConditions(param)) {
             * log.info("查询条件为空，使用所有BOM成本总览ID进行测试");
             * List<BomCostOverview> allBomCostOverviews = this.list();
             * bomCostOverviewIds = allBomCostOverviews.stream()
             * .map(BomCostOverview::getId)
             * .collect(Collectors.toList());
             * log.info("使用所有BOM成本总览ID: {}", bomCostOverviewIds);
             * } else {
             */
            // 如果有查询条件但没有结果，返回空结果
            Page<Scenario> emptyPage = new Page<>(param.getPageNo(), param.getPageSize());
            return new PageResult<>(emptyPage);
            /* } */
        }

        // 2. 使用Mapper的SQL分组分页查询
        log.info("执行SQL分组查询，BOM成本总览ID数量: {}, 正极体系过滤: {}",
                bomCostOverviewIds.size(), param.getPositiveElectrodeSystems());

        Page<Scenario> page = new Page<>(param.getPageNo(), param.getPageSize());
        // 将化学体系编号转换为正极材料核算ID
        List<Long> positiveMaterialAccountingIds = convertChemicalSystemCodesToIds(param.getPositiveElectrodeSystems());

        IPage<Scenario> pageResult = this.baseMapper.selectScenarioGroupPageList(
                page,
                bomCostOverviewIds,
                positiveMaterialAccountingIds);

        log.info("SQL查询完成，返回记录数: {}", pageResult.getRecords().size());

        // 3. 处理化学体系编号聚合
        List<Scenario> scenarios = pageResult.getRecords();
        aggregateChemicalSystemCodes(scenarios, bomCostOverviewIds);
        getBomCostOverviewCount(scenarios,bomCostOverviewIds);

        log.info("SQL层场景分组查询完成，总记录数: {}, 当前页记录数: {}",
                pageResult.getTotal(), scenarios.size());

        // 转换为PageResult
        PageResult<Scenario> result = new PageResult<>();
        result.setRows(scenarios);
        result.setTotalRows((int) pageResult.getTotal());
        result.setPageNo((int) pageResult.getCurrent());
        result.setPageSize((int) pageResult.getSize());

        // 内存排序：先按bomCostOverviewId降序，然后按scenarioId升序
        List<Scenario> records = result.getRows();
        records.sort(Comparator
                .comparing(Scenario::getBomCostOverviewId, Comparator.reverseOrder())
                .thenComparing(Scenario::getScenarioId, Comparator.nullsLast(Comparator.naturalOrder())));

        log.debug("场景分组分页查询结果排序完成，共 {} 条记录", records.size());

        return result;
    }

    /**
     * 根据查询条件获取符合条件的BOM成本总览ID列表
     */
    private List<Long> getBomCostOverviewIdsByConditions(BomCostOverview param) {
        log.info("开始查询BOM成本总览ID，查询条件: {}", param);

        LambdaQueryWrapper<BomCostOverview> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询ID字段
        queryWrapper.select(BomCostOverview::getId);

        // 核算代码模糊查询
        if (StrUtil.isNotBlank(param.getAccountingCode())) {
            queryWrapper.like(BomCostOverview::getAccountingCode, param.getAccountingCode());
            log.info("添加核算代码查询条件: {}", param.getAccountingCode());
        }

        // 产品名称模糊查询
        if (StrUtil.isNotBlank(param.getProductName())) {
            queryWrapper.like(BomCostOverview::getProductName, param.getProductName());
            log.info("添加产品名称查询条件: {}", param.getProductName());
        }

        // BOM文件编号模糊查询
        if (StrUtil.isNotBlank(param.getBomFileNumber())) {
            queryWrapper.like(BomCostOverview::getBomFileNumber, param.getBomFileNumber());
            log.info("添加BOM文件编号查询条件: {}", param.getBomFileNumber());
        }

        // 研究所查询
        if (ObjectUtil.isNotEmpty(param.getDepts())) {
            queryWrapper.in(BomCostOverview::getDept, param.getDepts());
            log.info("添加研究所查询条件: {}", param.getDepts());
        }

        // 客户类型查询
        if (ObjectUtil.isNotNull(param.getCustomerType())) {
            queryWrapper.eq(BomCostOverview::getCustomerType, param.getCustomerType());
            log.info("添加客户类型查询条件: {}", param.getCustomerType());
        }

        // 产品状态查询
        if (ObjectUtil.isNotEmpty(param.getProductStatuses())) {
            queryWrapper.in(BomCostOverview::getProductStatus, param.getProductStatuses());
            log.info("添加产品状态查询条件: {}", param.getProductStatuses());
        }

        List<BomCostOverview> bomCostOverviews = this.list(queryWrapper);
        List<Long> ids = bomCostOverviews.stream()
                .map(BomCostOverview::getId)
                .collect(Collectors.toList());

        log.info("BOM成本总览查询完成，找到{}条记录，ID列表: {}", ids.size(), ids);
        return ids;
    }

    /*
     * 获取bom成本总览的场景组
     */
    private void getBomCostOverviewCount(List<Scenario> scenarios, List<Long> bomCostOverviewIds) {

        List<Scenario> _scenarios = scenarioService
                .list(Wrappers.<Scenario>lambdaQuery().in(Scenario::getBomCostOverviewId, bomCostOverviewIds));
        List<Long> detailIds = _scenarios.stream().map(Scenario::getBomAccountingDetailId).collect(Collectors.toList());
        List<BomAccountingDetail> bomAccountingDetails = bomAccountingDetailService
                .list(Wrappers.<BomAccountingDetail>lambdaQuery().in(BomAccountingDetail::getId, detailIds));

        scenarios.forEach(e -> {
            List<Scenario> tmpScenarios = _scenarios.stream()
                    .filter(s -> s.getBomCostOverviewId().equals(e.getBomCostOverviewId()))
                    .filter(s -> s.getScenarioId().equals(e.getScenarioId())).collect(Collectors.toList());
            List<Long> tmpDetailIds = tmpScenarios.stream().map(Scenario::getBomAccountingDetailId)
                    .collect(Collectors.toList());
            List<BomAccountingDetail> tmpBomAccountingDetails = bomAccountingDetails.stream()
                    .filter(d -> tmpDetailIds.contains(d.getId())).collect(Collectors.toList());

            List<Long> structDetailIds = tmpBomAccountingDetails.stream().filter(d -> d.getStructureFlag().equals(1))
                    .map(BomAccountingDetail::getId).collect(Collectors.toList());
            List<Long> notStrutDetailIds = tmpBomAccountingDetails.stream().filter(d -> d.getStructureFlag().equals(0))
                    .map(BomAccountingDetail::getId).collect(Collectors.toList());

            e.setStructurePriceTotal(
                    tmpScenarios.stream().filter(s -> structDetailIds.contains(s.getBomAccountingDetailId()))
                            .map(Scenario::getAmountCnyWh).reduce(BigDecimal.ZERO, BigDecimal::add));

            e.setChemicalElementPriceTotal(
                    tmpScenarios.stream().filter(s -> notStrutDetailIds.contains(s.getBomAccountingDetailId()))
                            .map(Scenario::getAmountCnyWh).reduce(BigDecimal.ZERO, BigDecimal::add));
            e.setPriceTotal(
                    tmpScenarios.stream().map(Scenario::getAmountCnyWh).reduce(BigDecimal.ZERO, BigDecimal::add));
        });

    }

    /**
     * 为分组后的场景聚合化学体系编号
     */
    private void aggregateChemicalSystemCodes(List<Scenario> scenarios, List<Long> bomCostOverviewIds) {
        if (scenarios.isEmpty()) {
            return;
        }

        log.info("开始为{}条场景记录聚合化学体系编号（按bomCostOverviewId+scenarioId分组，已过滤逻辑删除）", scenarios.size());

        // 查询所有相关场景的化学体系编号（通过关联查询获取）
        LambdaQueryWrapper<Scenario> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Scenario::getScenarioId, Scenario::getPositiveMaterialAccountingId, Scenario::getBomCostOverviewId);
        //queryWrapper.eq(Scenario::getDeleteStatus, 0); // 过滤逻辑删除
        queryWrapper.in(Scenario::getBomCostOverviewId, bomCostOverviewIds);
        queryWrapper.isNotNull(Scenario::getPositiveMaterialAccountingId);

        List<Scenario> allScenarios = scenarioService.list(queryWrapper);

        // 填充化学体系信息
        fillChemicalSystemInfoForScenarios(allScenarios);

        // 按bomCostOverviewId + scenarioId分组
        Map<String, List<String>> scenarioChemicalSystemMap = allScenarios.stream()
                .filter(s -> s.getScenarioId() != null &&
                        s.getBomCostOverviewId() != null &&
                        StrUtil.isNotBlank(s.getChemicalSystemCode()) &&
                        !"null".equals(s.getChemicalSystemCode())) // 排除字符串'null'
                .collect(Collectors.groupingBy(
                        s -> s.getBomCostOverviewId() + "_" + s.getScenarioId(), // 组合键
                        Collectors.mapping(
                                Scenario::getChemicalSystemCode,
                                Collectors.toList())));

        // 为每个场景设置聚合的化学体系编号（通过positiveMaterialAccountingId查询）
        for (Scenario scenario : scenarios) {
            if (scenario.getScenarioId() != null && scenario.getBomCostOverviewId() != null) {
                // 构建组合键
                String groupKey = scenario.getBomCostOverviewId() + "_" + scenario.getScenarioId();
                List<String> chemicalSystemCodes = scenarioChemicalSystemMap.get(groupKey);

                if (chemicalSystemCodes != null && !chemicalSystemCodes.isEmpty()) {
                    // 去重并用分号连接
                    String aggregatedCodes = chemicalSystemCodes.stream()
                            .distinct()
                            .collect(Collectors.joining(";"));
                    scenario.setChemicalSystemCode(aggregatedCodes);

                    log.debug("BOM成本总览ID: {}, 场景ID: {}, 聚合化学体系: {}",
                            scenario.getBomCostOverviewId(), scenario.getScenarioId(), aggregatedCodes);
                }
            }
        }

        log.info("化学体系编号聚合完成");
    }

    /**
     * 判断查询条件是否为空
     */
    private boolean isEmptyQueryConditions(BomCostOverview param) {
        return StrUtil.isBlank(param.getAccountingCode()) &&
                StrUtil.isBlank(param.getProductName()) &&
                StrUtil.isBlank(param.getBomFileNumber()) &&
                ObjectUtil.isEmpty(param.getDepts()) &&
                ObjectUtil.isNull(param.getCustomerType()) &&
                ObjectUtil.isEmpty(param.getProductStatuses()) &&
                ObjectUtil.isEmpty(param.getPositiveElectrodeSystems());
    }

    @Override
    public List<BomCostOverview> list(BomCostOverview param) {
        LambdaQueryWrapper<BomCostOverview> queryWrapper = new LambdaQueryWrapper<>();

        // 核算代码模糊查询
        if (StrUtil.isNotBlank(param.getAccountingCode())) {
            queryWrapper.like(BomCostOverview::getAccountingCode, param.getAccountingCode());
        }

        // 产品名称模糊查询
        if (StrUtil.isNotBlank(param.getProductName())) {
            queryWrapper.like(BomCostOverview::getProductName, param.getProductName());
        }

        // 正极体系查询
        /*
         * if (ObjectUtil.isNotEmpty(param.getPositiveElectrodeSystems())) {
         * 
         * List<Long> ids = bomAccountingDetailService
         * .list(
         * Wrappers.lambdaQuery(BomAccountingDetail.class)
         * .in(BomAccountingDetail::getChemicalSystemCode,
         * param.getPositiveElectrodeSystems())
         * )
         * .stream().map(BomAccountingDetail::getBomCostOverviewId).distinct().collect(
         * Collectors.toList());
         * 
         * if (ids.size()<= 1000) {
         * queryWrapper.in(BomCostOverview::getId,ids);
         * }else{
         * List<List<Long>> partitionList = Lists.partition(ids, 1000);
         * queryWrapper.and(x -> {
         * for (List<Long> idPartition : partitionList) {
         * x.or().in(BomCostOverview::getId, idPartition);
         * }
         * });
         * }
         * }
         */

        // BOM文件编号模糊查询
        if (StrUtil.isNotBlank(param.getBomFileNumber())) {
            queryWrapper.like(BomCostOverview::getBomFileNumber, param.getBomFileNumber());
        }

        // 研究所查询
        if (ObjectUtil.isNotEmpty(param.getDepts())) {
            queryWrapper.in(BomCostOverview::getDept, param.getDepts());
        }

        // 客户类型查询
        if (ObjectUtil.isNotNull(param.getCustomerType())) {
            queryWrapper.eq(BomCostOverview::getCustomerType, param.getCustomerType());
        }

        // 产品状态查询
        if (ObjectUtil.isNotEmpty(param.getProductStatuses())) {
            queryWrapper.in(BomCostOverview::getProductStatus, param.getProductStatuses());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(BomCostOverview::getCreateTime);

        return this.list(queryWrapper);
    }

    @Resource
    private ISysBomService bomService;

    @Override
    @Transactional
    public void add(BomCostOverview param,String token) throws Exception {

        if (ObjectUtil.isNull(param.getANodeItems())) {
            throw new ServiceException(500, "化学体系不能为空");
        }

        /* if(ObjectUtil.isNotNull(param.getAuditStatus()) && param.getAuditStatus() == 1){
            param.setAuditStatus(1);
        } */

        param.setAuditStatus(Optional.ofNullable(param).map(BomCostOverview::getAuditStatus).orElse(0));
        param.setAccountingType(2);// 盐价
        param.setRatedEnergy(
                param.getRatedCapacity().multiply(param.getRatedVoltage()).setScale(3, BigDecimal.ROUND_HALF_UP));
        
        Long bomFileId = param.getBomFileId();

        if (null != param.getBomId() &&!param.getBomId().equals(0L) && (null == param.getBomFileId() || param.getBomFileId().equals(0L))) {
            SysBom bom = bomService.getOne(Wrappers.lambdaQuery(SysBom.class).eq(SysBom::getId, param.getBomId()));  
            param.setBomFileId(Optional.ofNullable(bom).map(SysBom::getFileId).orElse(0L));
        }
        // 根据PositiveMaterialAccountingId获取化学体系编号并设置正极体系
        List<Long> accountingIds = param.getANodeItems().stream()
            .map(PositiveType2PartNo::getPositiveMaterialAccountingId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        if (!accountingIds.isEmpty()) {
            List<PositiveMaterialAccounting> accountingList = positiveMaterialAccountingService.list(
                Wrappers.<PositiveMaterialAccounting>lambdaQuery()
                    .in(PositiveMaterialAccounting::getId, accountingIds)
                    .select(PositiveMaterialAccounting::getChemicalSystem)
            );

            String positiveElectrodeSystem = accountingList.stream()
                .map(PositiveMaterialAccounting::getChemicalSystem)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.joining("&"));

            param.setPositiveElectrodeSystem(positiveElectrodeSystem);
        } else {
            param.setPositiveElectrodeSystem("");
        }

        //param.setSeq(this.count() + 1);

        this.save(param);

        if (null != bomFileId) {
            bomAccountingDetailService.importfromExcel(param);
        }else{
            bomAccountingDetailService.importfromBom(param);
        }
        
        if(param.getAuditStatus() == 0){
            submit(param,token);
        }
    }

    public void submit(BomCostOverview item,String token){

        String serviceUrl = ConstantContextHolder.getSysConfigWithDefault("service_url", String.class, "");

        Map<Object, Object> _params = new HashMap<>();
        _params.put("productName", item.getProductName());
        _params.put("productStatus", item.getProductStatus());
        _params.put("bomCode", item.getBomFileNumber());
        _params.put("usageDescription", item.getUsageDescription());
        _params.put("majordomo", item.getMajordomo());//部门长
        _params.put("detailLink", serviceUrl + "bombill/account?id=" + item.getId());

        _params.put("ratedCapacity",item.getRatedCapacity());
        _params.put("ratedVoltage",item.getRatedVoltage());
        _params.put("attachmentId",item.getBomFileId());
        _params.put("attachmentName",null != item.getBomFileName() ? item.getBomFileName() : item.getBomFileNumber() );

        Map<String, Object> params = new HashMap<String, Object>(4) {
            {
                put("userName", item.getRequesterJobNumber());
                put("map", _params);
            }
        };

        JSONObject resp = Utils.doPostAndToken(JiraApiParams.createBomCost, token, params);

        if (resp.getBoolean("result")) {

            BomCostParam bomCostParam = JSONObject.parseObject(resp.getString("value"),BomCostParam.class);
            item.setCostIssueId(bomCostParam.getIssueId());
            item.setIssueKey(bomCostParam.getIssueKey());
            this.updateById(item);

        } else {
            throw new ServiceException(500, resp.getString("message"));
        }
    }
    
    public Boolean auditBomCost(BomCostParam param){

        if(ObjectUtil.isEmpty(param) || ObjectUtil.isEmpty(param.getIssueId())){
            return false;
        }

        BomCostOverview one = this.getOne(Wrappers.<BomCostOverview>lambdaQuery().eq(BomCostOverview::getCostIssueId, param.getIssueId()));

        if (param.getHandleResult() == 10) {
            one.setAuditStatus(1);
            this.update(one);
            return true;
        }

        one.setAuditStatus(3);
        this.update(one);

        return true;
    }

    @Override
    public Boolean delete(BomCostOverview param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        return this.removeById(param.getId());
    }

    @Override
    public Boolean update(BomCostOverview param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        BomCostOverview updateEntity = BomCostOverview.builder()
                .id(param.getId())
                .accountingCode(param.getAccountingCode())
                .accountingDate(param.getAccountingDate())
                .customerType(param.getCustomerType())
                .productName(param.getProductName())
                // .positiveElectrodeSystem(param.getPositiveElectrodeSystem())
                .ratedCapacity(param.getRatedCapacity())
                .ratedVoltage(param.getRatedVoltage())
                .ratedEnergy(param.getRatedEnergy())
                .usageDescription(param.getUsageDescription())
                .bomFileNumber(param.getBomFileNumber())
                .bomFileId(param.getBomFileId())
                .requesterJobNumber(param.getRequesterJobNumber())
                .requesterName(param.getRequesterName())
                .dept(param.getDept())
                .productStatus(param.getProductStatus())
                .auditStatus(param.getAuditStatus())
                .accountingType(param.getAccountingType())
                .issueId(param.getIssueId())
                .bomId(param.getBomId())
                .build();
        return this.updateById(updateEntity);
    }

    @Override
    public void finish(BomCostOverview param) {

        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        BomCostOverview one = this.getById(param.getId());
        BomCostOverview updateEntity = BomCostOverview.builder()
                .id(param.getId())
                .accountingCode(generateAccountingCode(one.getDept()))
                .auditStatus(2)
                .accountingDate(new Date())
                .build();
        this.updateById(updateEntity);
    }

    @Override
    public BomCostOverview get(BomCostOverview param) {
        if (ObjectUtil.isEmpty(param.getId())) {
            throw new ServiceException(400, "ID必填");
        }
        BomCostOverview result = this.getById(param.getId());

        // 填充aNodeItems中的化学体系编号信息
        if (result != null && result.getANodeItems() != null) {
            fillChemicalSystemCodeForANodeItems(result.getANodeItems());
        }

        return result;
    }

    @Override
    public List<Scenario> scenarioGroupList(BomCostOverview param) {
        log.info("开始场景分组列表查询（非分页），查询参数: {}", param);

        try {
            // 1. 根据BOM成本总览的查询条件，先查询符合条件的BOM成本总览ID
            List<Long> bomCostOverviewIds = getBomCostOverviewIdsByConditions(param);

            if (bomCostOverviewIds.isEmpty()) {
                log.warn("没有找到符合条件的BOM成本总览记录");
                return new ArrayList<>();
            }

            log.debug("找到 {} 个符合条件的BOM成本总览ID: {}", bomCostOverviewIds.size(), bomCostOverviewIds);

            // 2. 调用Mapper的非分页方法
            // 将化学体系编号转换为正极材料核算ID
            List<Long> positiveMaterialAccountingIds = convertChemicalSystemCodesToIds(param.getPositiveElectrodeSystems());

            List<Scenario> scenarios = this.baseMapper.selectScenarioGroupList(
                bomCostOverviewIds,
                positiveMaterialAccountingIds
            );

            log.info("SQL查询完成，返回记录数: {}", scenarios.size());

            // 3. 处理化学体系编号聚合
            aggregateChemicalSystemCodes(scenarios, bomCostOverviewIds);
            getBomCostOverviewCount(scenarios, bomCostOverviewIds);

            log.info("场景分组列表查询完成，共查询到 {} 条记录", scenarios.size());

            return scenarios;

        } catch (Exception e) {
            log.error("场景分组列表查询失败", e);
            throw new ServiceException(500, "查询失败：" + e.getMessage());
        }
    }

    @Override
    public void exportExcel(BomCostOverview param, HttpServletResponse response) {
        try {
            log.info("开始导出BOM成本总览Excel，查询参数: {}", param);

            // 1. 查询数据
            // 使用非分页的场景分组查询方法
            List<Scenario> scenarios = this.scenarioGroupList(param);

            if (scenarios.isEmpty()) {
                log.warn("没有找到符合条件的数据，导出空Excel");
            }

            // 2. 转换为Excel DTO
            List<BomCostOverviewExcelDto> excelDataList = BomCostOverviewExcelUtil.toExcelDtoList(scenarios);

            // 3. 生成文件名
            String fileName = "BOM成本总览表_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";

            // 4. 设置响应头
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" +
                URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

            // 5. 写入Excel
            EasyExcel.write(response.getOutputStream(), BomCostOverviewExcelDto.class)
                    .sheet("BOM成本总览")
                    .doWrite(excelDataList);

            log.info("BOM成本总览Excel导出成功，共导出 {} 条数据", excelDataList.size());

        } catch (Exception e) {
            log.error("BOM成本总览Excel导出失败", e);
            e.printStackTrace();

            // 导出失败时返回错误信息
            try {
                response.reset();
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copySelectedData(BomCostOverviewCopyParam param) {
        log.info("开始复制选中的BOM成本总览数据，参数: {}", param);

        if (param == null || param.getSelectedRows() == null || param.getSelectedRows().isEmpty()) {
            throw new ServiceException(400, "请选择要复制的数据");
        }

        try {
            // 1. 按bomCostOverviewId分组
            Map<Long, List<BomCostOverviewCopyParam.SelectedRowData>> groupedData = param.getSelectedRows().stream()
                .filter(row -> row.getBomCostOverviewId() != null && row.getScenarioId() != null)
                .collect(Collectors.groupingBy(BomCostOverviewCopyParam.SelectedRowData::getBomCostOverviewId));

            log.info("按bomCostOverviewId分组后，共有 {} 个BOM成本总览需要复制", groupedData.size());

            int totalBomCount = groupedData.size();
            int successBomCount = 0;

            // 2. 逐个BOM成本总览进行批量复制
            for (Map.Entry<Long, List<BomCostOverviewCopyParam.SelectedRowData>> entry : groupedData.entrySet()) {
                Long originalBomCostOverviewId = entry.getKey();
                List<BomCostOverviewCopyParam.SelectedRowData> selectedRows = entry.getValue();

                // 提取选中的场景ID
                Set<Long> selectedScenarioIds = selectedRows.stream()
                    .map(BomCostOverviewCopyParam.SelectedRowData::getScenarioId)
                    .collect(Collectors.toSet());

                log.info("开始复制第 {}/{} 个BOM成本总览 - ID: {}, 选中场景数: {}",
                    successBomCount + 1, totalBomCount, originalBomCostOverviewId, selectedScenarioIds.size());

                try {
                    // 复制单个BOM成本总览及其相关数据
                    copyBomCostOverviewWithRelatedData(originalBomCostOverviewId, selectedScenarioIds);
                    successBomCount++;
                    log.info("第 {}/{} 个BOM成本总览复制完成", successBomCount, totalBomCount);
                } catch (Exception e) {
                    log.error("复制BOM成本总览ID {} 失败: {}", originalBomCostOverviewId, e.getMessage(), e);
                    // 继续处理下一个，不中断整个流程
                }
            }

            log.info("复制选中的BOM成本总览数据完成，成功复制 {}/{} 个BOM成本总览", successBomCount, totalBomCount);

            if (successBomCount == 0) {
                throw new ServiceException(400, "没有成功复制任何BOM成本总览，请检查选中的数据是否有效");
            }

        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("复制选中的BOM成本总览数据失败", e);
            throw new ServiceException(500, "复制失败：" + e.getMessage());
        }
    }

    /**
     * 复制单个BOM成本总览及其相关数据
     *
     * @param originalBomCostOverviewId 原始BOM成本总览ID
     * @param selectedScenarioIds 选中的场景ID集合
     */
    private void copyBomCostOverviewWithRelatedData(Long originalBomCostOverviewId, Set<Long> selectedScenarioIds) {
        log.info("开始复制BOM成本总览ID: {}, 选中场景: {}", originalBomCostOverviewId, selectedScenarioIds);

        // 1. 复制BomCostOverview数据
        BomCostOverview originalBomCostOverview = this.getById(originalBomCostOverviewId);
        if (originalBomCostOverview == null) {
            throw new ServiceException(400, "BOM成本总览ID " + originalBomCostOverviewId + " 不存在");
        }

        BomCostOverview newBomCostOverview = copyBomCostOverview(originalBomCostOverview);
        this.save(newBomCostOverview);
        log.info("复制BomCostOverview成功，原ID: {} -> 新ID: {}", originalBomCostOverviewId, newBomCostOverview.getId());

        // 2. 复制BomAccountingDetail数据（复制所有明细）
        List<BomAccountingDetail> originalDetails = bomAccountingDetailService.list(
            Wrappers.<BomAccountingDetail>lambdaQuery()
                .eq(BomAccountingDetail::getBomCostOverviewId, originalBomCostOverviewId)
        );

        List<BomAccountingDetail> newDetails = copyBomAccountingDetails(originalDetails, newBomCostOverview.getId());
        if (!newDetails.isEmpty()) {
            bomAccountingDetailService.saveBatch(newDetails);
            log.info("复制BomAccountingDetail成功，共 {} 条", newDetails.size());
        }

        // 3. 复制选中的Scenario数据
        List<Scenario> originalScenarios = scenarioService.list(
            Wrappers.<Scenario>lambdaQuery()
                .eq(Scenario::getBomCostOverviewId, originalBomCostOverviewId)
                .in(Scenario::getScenarioId, selectedScenarioIds)
        );

        List<Scenario> newScenarios = copyScenarios(originalScenarios, newBomCostOverview.getId(), newDetails);
        if (!newScenarios.isEmpty()) {
            scenarioService.saveBatch(newScenarios);
            log.info("复制Scenario成功，共 {} 条", newScenarios.size());
        }

        // 4. 复制选中场景的ChemicalElementPrice数据
        for (Long scenarioId : selectedScenarioIds) {
            List<ChemicalElementPrice> originalPrices = chemicalElementPriceService.list(
                Wrappers.<ChemicalElementPrice>lambdaQuery()
                    .eq(ChemicalElementPrice::getBomCostOverviewId, originalBomCostOverviewId)
                    .eq(ChemicalElementPrice::getScenarioId, scenarioId)
            );

            List<ChemicalElementPrice> newPrices = copyChemicalElementPrices(originalPrices, newBomCostOverview.getId(), scenarioId);
            if (!newPrices.isEmpty()) {
                chemicalElementPriceService.saveBatch(newPrices);
                log.info("复制场景ID {} 的ChemicalElementPrice成功，共 {} 条", scenarioId, newPrices.size());
            }
        }

        log.info("BOM成本总览ID {} 及其相关数据复制完成", originalBomCostOverviewId);
    }

    /**
     * 复制BomCostOverview对象
     */
    private BomCostOverview copyBomCostOverview(BomCostOverview original) {
        //BomCostOverview copy = new BomCostOverview();
        //copy.setAccountingCode(generateNewAccountingCode(original.getAccountingCode()));
        //copy.setAccountingDate(new Date());
        /* copy.setCustomerType(original.getCustomerType());
        copy.setProductName(original.getProductName());
        copy.setRatedCapacity(original.getRatedCapacity());
        copy.setRatedVoltage(original.getRatedVoltage());
        copy.setRatedEnergy(original.getRatedEnergy());
        copy.setUsageDescription(original.getUsageDescription());
        copy.setBomFileNumber(original.getBomFileNumber());
        copy.setBomFileId(original.getBomFileId());
        copy.setRequesterJobNumber(original.getRequesterJobNumber());
        copy.setRequesterName(original.getRequesterName());
        copy.setDept(original.getDept());
        copy.setProductStatus(original.getProductStatus());
        copy.setAuditStatus(1); // 新复制的数据设为审核中状态
        copy.setAccountingType(original.getAccountingType());
        copy.setIssueId(original.getIssueId());
        copy.setBomId(original.getBomId());
        copy.setPositiveElectrodeSystem(original.getPositiveElectrodeSystem()); */
        original.setId(null);
        original.setSeq(this.count() + 1);
        original.setAuditStatus(1);
        return original;
    }

    /**
     * 生成新的核算代码
     */
    private String generateNewAccountingCode(String originalCode) {
        if (StrUtil.isBlank(originalCode)) {
            return "COPY_" + System.currentTimeMillis();
        }
        return originalCode + "_COPY_" + DateUtil.format(new Date(), "yyyyMMddHHmmss");
    }

    /**
     * 复制BomAccountingDetail列表
     */
    private List<BomAccountingDetail> copyBomAccountingDetails(List<BomAccountingDetail> originalList, Long newBomCostOverviewId) {
        //List<BomAccountingDetail> newList = new ArrayList<>();
        for (BomAccountingDetail original : originalList) {
            original.setOldId(original.getId());
            original.setId(null);
            original.setBomCostOverviewId(newBomCostOverviewId);
            /* BomAccountingDetail copy = new BomAccountingDetail();
            copy.setBomCostOverviewId(newBomCostOverviewId);
            copy.setPartNumber(original.getPartNumber());
            copy.setPartName(original.getPartName());
            copy.setPartDescription(original.getPartDescription());
            copy.setPartUnit(original.getPartUnit());
            copy.setBaseUse(original.getBaseUse());
            copy.setProcessingFee(original.getProcessingFee());
            copy.setStructureFlag(original.getStructureFlag());
            copy.setMaterialType(original.getMaterialType());
            copy.setPositiveMaterialAccountingId(original.getPositiveMaterialAccountingId());
            newList.add(copy); */
        }
        return originalList;
        
    }

    /**
     * 复制Scenario列表
     */
    private List<Scenario> copyScenarios(List<Scenario> originalList, Long newBomCostOverviewId, List<BomAccountingDetail> newDetails) {
        //List<Scenario> newList = new ArrayList<>();

        // 创建原始BomAccountingDetailId到新BomAccountingDetailId的映射
        /* Map<String, Long> partNumberToNewDetailIdMap = new HashMap<>();
        for (BomAccountingDetail detail : newDetails) {
            partNumberToNewDetailIdMap.put(detail.getPartNumber(), detail.getId());
        } */

        for (Scenario original : originalList) {
            // 根据partNumber找到对应的新BomAccountingDetailId
            /* BomAccountingDetail originalDetail = bomAccountingDetailService.getById(original.getBomAccountingDetailId());
            if (originalDetail == null) {
                log.warn("找不到原始BomAccountingDetail，ID: {}", original.getBomAccountingDetailId());
                continue;
            }

            Long newBomAccountingDetailId = partNumberToNewDetailIdMap.get(originalDetail.getPartNumber());
            if (newBomAccountingDetailId == null) {
                log.warn("找不到对应的新BomAccountingDetailId，partNumber: {}", originalDetail.getPartNumber());
                continue;
            } */

            /* Scenario copy = new Scenario();
            copy.setPositiveMaterialAccountingId(original.getPositiveMaterialAccountingId());
            copy.setScenarioId(original.getScenarioId()); */
            BomAccountingDetail originalDetail = newDetails.stream().filter(d -> d.getOldId().equals(original.getBomAccountingDetailId())).findFirst().orElse(null);
            if (null == originalDetail) {
                continue;
            }
            original.setId(null);
            original.setBomCostOverviewId(newBomCostOverviewId);
            original.setBomAccountingDetailId(originalDetail.getId());
            /* copy.setUntaxedUnitPrice(original.getUntaxedUnitPrice());
            copy.setAmountCnyEa(original.getAmountCnyEa());
            copy.setAmountCnyWh(original.getAmountCnyWh());
            copy.setProportion(original.getProportion());
            copy.setBaseUse(original.getBaseUse());
            newList.add(copy); */
        }
        return originalList;
    }

    /**
     * 复制ChemicalElementPrice列表
     */
    private List<ChemicalElementPrice> copyChemicalElementPrices(List<ChemicalElementPrice> originalList, Long newBomCostOverviewId, Long scenarioId) {
        //List<ChemicalElementPrice> newList = new ArrayList<>();
        for (ChemicalElementPrice original : originalList) {
            original.setId(null);
            original.setBomCostOverviewId(newBomCostOverviewId);
            original.setScenarioId(scenarioId);
            /* ChemicalElementPrice copy = new ChemicalElementPrice();
            copy.setScenarioId(scenarioId);
            copy.setBomCostOverviewId(newBomCostOverviewId);
            copy.setChemicalElementId(original.getChemicalElementId());
            copy.setPositiveElectrodeAccountingType(original.getPositiveElectrodeAccountingType());
            copy.setCnyPrice(original.getCnyPrice());
            copy.setForeignPrice(original.getForeignPrice());
            copy.setCurrencyType(original.getCurrencyType());
            copy.setUnit(original.getUnit());
            newList.add(copy); */
        }
        return originalList;
    }

    /**
     * 将化学体系编号转换为正极材料核算ID
     *
     * @param chemicalSystemCodes 化学体系编号列表
     * @return 正极材料核算ID列表
     */
    private List<Long> convertChemicalSystemCodesToIds(List<String> chemicalSystemCodes) {
        if (chemicalSystemCodes == null || chemicalSystemCodes.isEmpty()) {
            return new ArrayList<>();
        }

        // 查询正极材料核算表，根据化学体系编号获取ID
        List<PositiveMaterialAccounting> accountingList = positiveMaterialAccountingService.list(
            Wrappers.<PositiveMaterialAccounting>lambdaQuery()
                .in(PositiveMaterialAccounting::getChemicalSystemCode, chemicalSystemCodes)
                .select(PositiveMaterialAccounting::getId)
        );

        return accountingList.stream()
            .map(PositiveMaterialAccounting::getId)
            .collect(Collectors.toList());
    }

    /**
     * 为场景列表填充化学体系信息
     * 根据positiveMaterialAccountingId查询对应的化学体系编号和名称
     */
    private void fillChemicalSystemInfoForScenarios(List<Scenario> scenarios) {
        if (scenarios == null || scenarios.isEmpty()) {
            return;
        }

        // 收集所有的positiveMaterialAccountingId
        Set<Long> accountingIds = scenarios.stream()
            .map(Scenario::getPositiveMaterialAccountingId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (accountingIds.isEmpty()) {
            return;
        }

        // 批量查询PositiveMaterialAccounting信息
        List<PositiveMaterialAccounting> accountingList = positiveMaterialAccountingService.list(
            Wrappers.<PositiveMaterialAccounting>lambdaQuery()
                .in(PositiveMaterialAccounting::getId, accountingIds)
        );

        // 创建ID到对象的映射
        Map<Long, PositiveMaterialAccounting> accountingMap = accountingList.stream()
            .collect(Collectors.toMap(PositiveMaterialAccounting::getId, Function.identity()));

        // 填充化学体系信息
        scenarios.forEach(scenario -> {
            if (scenario.getPositiveMaterialAccountingId() != null) {
                PositiveMaterialAccounting accounting = accountingMap.get(scenario.getPositiveMaterialAccountingId());
                if (accounting != null) {
                    scenario.setChemicalSystemCode(accounting.getChemicalSystemCode());
                    scenario.setChemicalSystem(accounting.getChemicalSystem());
                }
            }
        });
    }

    /**
     * 为aNodeItems填充化学体系编号信息
     * 根据positiveMaterialAccountingId查询对应的化学体系编号
     */
    private void fillChemicalSystemCodeForANodeItems(List<PositiveType2PartNo> aNodeItems) {
        if (aNodeItems == null || aNodeItems.isEmpty()) {
            return;
        }

        // 收集所有的positiveMaterialAccountingId
        Set<Long> accountingIds = aNodeItems.stream()
            .map(PositiveType2PartNo::getPositiveMaterialAccountingId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (accountingIds.isEmpty()) {
            return;
        }

        // 批量查询PositiveMaterialAccounting信息
        List<PositiveMaterialAccounting> accountingList = positiveMaterialAccountingService.list(
            Wrappers.<PositiveMaterialAccounting>lambdaQuery()
                .in(PositiveMaterialAccounting::getId, accountingIds)
                .select(PositiveMaterialAccounting::getId, PositiveMaterialAccounting::getChemicalSystemCode)
        );

        // 创建ID到化学体系编号的映射
        Map<Long, String> accountingMap = accountingList.stream()
            .collect(Collectors.toMap(PositiveMaterialAccounting::getId, PositiveMaterialAccounting::getChemicalSystemCode));

        // 填充化学体系编号信息
        aNodeItems.forEach(item -> {
            if (item.getPositiveMaterialAccountingId() != null) {
                String chemicalSystemCode = accountingMap.get(item.getPositiveMaterialAccountingId());
                if (StrUtil.isNotBlank(chemicalSystemCode)) {
                    item.setChemicalSystemCode(chemicalSystemCode);
                }
            }
        });
    }

    @Override
    public List<BomCostCompareResult> compareScenarios(BomCostCompareParam param) {

        List<BomCostCompareResult> results = new ArrayList<>();

        // 获取场景对比信息
        List<BomCostCompareParam.ScenarioCompareInfo> scenarios = param.getScenarios();
        BomCostCompareParam.ScenarioCompareInfo baselineScenario = param.getBaselineScenario();

        if (scenarios == null || scenarios.isEmpty()) {
            return results;
        }

        if (baselineScenario == null) {
            return results;
        }

        // 无差别获取场景
        List<Scenario> _scenarios = scenarioService.list(
            Wrappers.<Scenario>lambdaQuery()
                .in(Scenario::getBomCostOverviewId, scenarios.stream().map(BomCostCompareParam.ScenarioCompareInfo::getBomCostOverviewId).collect(Collectors.toList()))
                .in(Scenario::getScenarioId, scenarios.stream().map(BomCostCompareParam.ScenarioCompareInfo::getScenarioId).collect(Collectors.toList()))
        );

        // 获取过滤后的场景
        List<Scenario> filterScenarios = new ArrayList<>();
        scenarios.forEach(e->{
            filterScenarios.addAll(
                _scenarios.stream()
                .filter(s->s.getScenarioId().equals(e.getScenarioId()))
                .filter(s->s.getBomCostOverviewId().equals(e.getBomCostOverviewId()))
                .collect(Collectors.toList())
            );
        });

        // 获取BOM成本总览
        List<BomCostOverview> bomCostOverviews = this.listByIds(scenarios.stream().map(BomCostCompareParam.ScenarioCompareInfo::getBomCostOverviewId).collect(Collectors.toSet()));

        // 设置核算类型
        scenarios.forEach(e->{
            e.setAccountType(bomCostOverviews.stream().filter(s-> s.getId().equals(e.getBomCostOverviewId())).findFirst().get().getAccountingType());
        });

        // 无差别获取化学元素价格
        List<ChemicalElementPrice> chemicalElementPrices = chemicalElementPriceService.list(
            Wrappers.<ChemicalElementPrice>lambdaQuery()
                .in(ChemicalElementPrice::getBomCostOverviewId, scenarios.stream().map(BomCostCompareParam.ScenarioCompareInfo::getBomCostOverviewId).collect(Collectors.toList()))
                .in(ChemicalElementPrice::getScenarioId, scenarios.stream().map(BomCostCompareParam.ScenarioCompareInfo::getScenarioId).collect(Collectors.toList()))
                .in(ChemicalElementPrice::getPositiveElectrodeAccountingType, scenarios.stream().map(BomCostCompareParam.ScenarioCompareInfo::getAccountType).collect(Collectors.toList()))
        );

        // 获取过滤后的化学元素价格 BomCostOverviewId+scenarioId+accountType
        List<ChemicalElementPrice> filterChemicalElementPrices = new ArrayList<>();
        scenarios.forEach(e->{
            filterChemicalElementPrices.addAll(
                chemicalElementPrices.stream()
                .filter(s->s.getBomCostOverviewId().equals(e.getBomCostOverviewId()))
                .filter(s->s.getScenarioId().equals(e.getScenarioId()))
                .filter(s->s.getPositiveElectrodeAccountingType().equals(e.getAccountType()))
                .collect(Collectors.toList())
            );
        });

        // 获取bom核算明细
        List<BomAccountingDetail> bomAccountingDetails = bomAccountingDetailService.list(
            Wrappers.<BomAccountingDetail>lambdaQuery()
                .in(BomAccountingDetail::getBomCostOverviewId, scenarios.stream().map(BomCostCompareParam.ScenarioCompareInfo::getBomCostOverviewId).collect(Collectors.toList()))
        );

        List<ChemicalElement> chemicalElements = chemicalElementService.list();

        List<Long> filterChemicalElementIds = chemicalElements.stream()
            .filter(s->s.getMetalPremiseDisplay() == 1)
            .map(ChemicalElement::getId)
            .collect(Collectors.toList());

        List<ChemicalElementPrice> allChemicalElementPrices = new ArrayList<>();

        for (BomCostCompareParam.ScenarioCompareInfo scenarioInfo : scenarios) {

            BomCostOverview bomCostOverview = bomCostOverviews.stream().filter(s-> s.getId().equals(scenarioInfo.getBomCostOverviewId())).findFirst().get();

            List<Scenario> scenarioList = filterScenarios.stream()
                .filter(s->s.getBomCostOverviewId().equals(scenarioInfo.getBomCostOverviewId()))
                .filter(s->s.getScenarioId().equals(scenarioInfo.getScenarioId())).collect(Collectors.toList());

            List<ChemicalElementPrice> chemicalElementPriceList = filterChemicalElementPrices.stream()
                .filter(s->s.getBomCostOverviewId().equals(scenarioInfo.getBomCostOverviewId()))
                .filter(s->s.getScenarioId().equals(scenarioInfo.getScenarioId()))
                .filter(s->s.getPositiveElectrodeAccountingType().equals(scenarioInfo.getAccountType()))
                .collect(Collectors.toList());

            List<ChemicalElementPrice> filterChemicalElementPriceList = new ArrayList<>();

            if (filterChemicalElementIds.size() > 0) {
                filterChemicalElementPriceList = chemicalElementPriceList.stream()
                .filter(e -> filterChemicalElementIds.contains(e.getChemicalElementId()))
                .collect(Collectors.toList());
            }


            if (filterChemicalElementPriceList.size() > 0) {
                allChemicalElementPrices.addAll(filterChemicalElementPriceList);
            }
            //allChemicalElementPrices.addAll(filterChemicalElementPriceList.size() > 0 ? filterChemicalElementPriceList: chemicalElementPriceList);


            BomCostCompareResult result = new BomCostCompareResult();
            result.setScenarioId(scenarioInfo.getScenarioId());
            result.setScenarioName("场景" + scenarioInfo.getScenarioId());
            result.setProductName(bomCostOverview.getProductName());
            result.setBomCostOverviewId(scenarioInfo.getBomCostOverviewId());
            result.setRatedEnergy(bomCostOverview.getRatedEnergy());

            BigDecimal totalCost = calculateTotalBomCostByGroup(scenarioList);
            BigDecimal structureCost = calculateStructureCostByGroup(scenarioList,bomAccountingDetails);
            BigDecimal cellCost = totalCost.subtract(structureCost); // 化学材料成本 = BOM总成本 - 结构件成本

            result.setCellCost(cellCost);
            result.setStructureCost(structureCost);
            result.setTotalCost(totalCost);

            results.add(result);

        }

        for (BomCostCompareResult result : results) {
            result.setChemicalPrices(
                allChemicalElementPrices.stream().collect(Collectors.toMap(
                    obj -> Arrays.asList(obj.getChemicalElementId(),obj.getCnyPrice()),
                    Function.identity(), // 值 = 对象本身
                    (existing, replacement) -> existing
                ))
                .values().stream()
                .map(
                    e->{
                        e.setElementName(chemicalElements.stream().filter(s->s.getId().equals(e.getChemicalElementId())).map(ChemicalElement::getElementName).findFirst().orElse(""));
                        return e;
                    }
                ).collect(Collectors.toList())
            );
        }

        return results;
    }

    @Override
    public List<MaterialCostResult> calculateMaterialCosts(BomCostCompareParam param) {
        List<MaterialCostResult> results = new ArrayList<>();

        // 获取场景对比信息
        List<BomCostCompareParam.ScenarioCompareInfo> scenarios = param.getScenarios();

        log.info("开始计算材料成本，场景数量: {}", scenarios != null ? scenarios.size() : 0);

        if (scenarios == null || scenarios.isEmpty()) {
            log.warn("场景列表为空");
            return results;
        }


        Map<Long, List<SysDictData>> dictMap= (Map<Long, List<SysDictData>>) ConstantContext.me().get("dict_map");
        List<SysDictData> dictDatas = dictMap.get(1937030577128042498L);
        //dictDatas = dictDatas.stream().sorted(Comparator.comparing(SysDictData::getSort)).collect(Collectors.toList());

        // 材料类型字典
        Map<Integer, String> materialTypeDict = dictDatas.stream().collect(Collectors.toMap(e->Integer.valueOf(e.getCode()), SysDictData::getValue));
        /* new HashMap<>();
        materialTypeDict.put(1, "石墨");
        materialTypeDict.put(2, "硅");
        materialTypeDict.put(3, "铝箔");
        materialTypeDict.put(4, "隔膜");
        materialTypeDict.put(5, "铜箔");
        materialTypeDict.put(7, "电解液");
        materialTypeDict.put(8, "其他材料"); */

        // 查询所有涉及的BOM成本总览
        Set<Long> bomCostOverviewIds = scenarios.stream()
            .map(BomCostCompareParam.ScenarioCompareInfo::getBomCostOverviewId)
            .collect(Collectors.toSet());

        List<BomCostOverview> bomCostOverviews = this.listByIds(bomCostOverviewIds);
        Map<Long, BomCostOverview> bomCostOverviewMap = bomCostOverviews.stream()
            .collect(Collectors.toMap(BomCostOverview::getId, b -> b));

        // 为每个场景计算材料成本
        for (BomCostCompareParam.ScenarioCompareInfo scenarioInfo : scenarios) {
            BomCostOverview bomCostOverview = bomCostOverviewMap.get(scenarioInfo.getBomCostOverviewId());
            if (bomCostOverview == null) {
                log.warn("未找到BOM成本总览: bomCostOverviewId={}", scenarioInfo.getBomCostOverviewId());
                continue;
            }

            MaterialCostResult result = new MaterialCostResult();
            result.setScenarioId(scenarioInfo.getScenarioId());
            result.setBomCostOverviewId(scenarioInfo.getBomCostOverviewId());
            result.setProductName(bomCostOverview.getProductName());
            result.setScenarioName("场景" + scenarioInfo.getScenarioId());
            result.setRatedEnergy(bomCostOverview.getRatedEnergy());

            // 计算各材料类型的成本
            Map<Integer, MaterialCostResult.MaterialCostDetail> materialCosts =
                calculateMaterialCostsByType(scenarioInfo.getBomCostOverviewId(),
                                           scenarioInfo.getScenarioId(),
                                           materialTypeDict,
                                           bomCostOverview.getRatedEnergy());

            result.setMaterialCosts(materialCosts);
            results.add(result);

            log.info("场景材料成本计算完成: bomCostOverviewId={}, scenarioId={}, 材料类型数量={}",
                scenarioInfo.getBomCostOverviewId(), scenarioInfo.getScenarioId(),
                materialCosts.size());
        }

        log.info("材料成本计算完成，返回结果数量: {}", results.size());
        return results;
    }

    /**
     * 根据材料类型计算成本
     */
    private Map<Integer, MaterialCostResult.MaterialCostDetail> calculateMaterialCostsByType(
            Long bomCostOverviewId, Long scenarioId, Map<Integer, String> materialTypeDict, BigDecimal ratedEnergy) {

        Map<Integer, MaterialCostResult.MaterialCostDetail> materialCosts = new HashMap<>();

        try {
            // 查询该bomCostOverviewId+scenarioId对应的场景数据
            LambdaQueryWrapper<Scenario> scenarioWrapper = new LambdaQueryWrapper<>();
            scenarioWrapper.eq(Scenario::getBomCostOverviewId, bomCostOverviewId)
                          .eq(Scenario::getScenarioId, scenarioId);

            List<Scenario> scenarios = scenarioService.list(scenarioWrapper);

            if (scenarios.isEmpty()) {
                log.warn("未找到场景数据: bomCostOverviewId={}, scenarioId={}", bomCostOverviewId, scenarioId);
                return materialCosts;
            }

            // 获取所有BomAccountingDetail ID
            Set<Long> bomAccountingDetailIds = scenarios.stream()
                .map(Scenario::getBomAccountingDetailId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            if (bomAccountingDetailIds.isEmpty()) {
                log.warn("未找到BOM核算明细ID: bomCostOverviewId={}, scenarioId={}", bomCostOverviewId, scenarioId);
                return materialCosts;
            }

            // 查询BOM核算明细
            List<BomAccountingDetail> bomAccountingDetails = bomAccountingDetailService.listByIds(bomAccountingDetailIds);
            Map<Long, BomAccountingDetail> detailMap = bomAccountingDetails.stream()
                .collect(Collectors.toMap(BomAccountingDetail::getId, d -> d));

            // 按材料类型分组计算成本
            for (Map.Entry<Integer, String> entry : materialTypeDict.entrySet()) {
                Integer materialTypeId = entry.getKey();
                String materialTypeName = entry.getValue();

                // 筛选该材料类型的场景数据，分别计算未税单价和成本
                List<Scenario> materialScenarios = scenarios.stream()
                    .filter(scenario -> {
                        BomAccountingDetail detail = detailMap.get(scenario.getBomAccountingDetailId());
                        return detail != null && materialTypeId.equals(detail.getMaterialType());
                    })
                    .collect(Collectors.toList());

                // 计算未税单价总和 - 直接使用Scenario表中的untaxedUnitPrice字段
                BigDecimal untaxedUnitPrice = materialScenarios.stream()
                    .filter(scenario -> scenario.getUntaxedUnitPrice() != null)
                    .map(Scenario::getUntaxedUnitPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 计算成本(CNY/Wh)总和
                BigDecimal totalCost = materialScenarios.stream()
                    .filter(scenario -> scenario.getAmountCnyWh() != null)
                    .map(Scenario::getAmountCnyWh)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 创建材料成本明细
                MaterialCostResult.MaterialCostDetail detail = new MaterialCostResult.MaterialCostDetail();
                detail.setMaterialTypeId(materialTypeId);
                detail.setMaterialTypeName(materialTypeName);
                detail.setUntaxedUnitPrice(untaxedUnitPrice);
                detail.setCostPerWh(totalCost);

                materialCosts.put(materialTypeId, detail);

                log.debug("材料类型成本计算: materialType={}, untaxedUnitPrice={}, costPerWh={}",
                    materialTypeName, untaxedUnitPrice, totalCost);
            }

        } catch (Exception e) {
            log.error("计算材料成本失败: bomCostOverviewId={}, scenarioId={}",
                bomCostOverviewId, scenarioId, e);
        }

        return materialCosts;
    }

    /**
     * 生成核算代码
     * 格式：部门-日期+流水号（基于当天完成核算的数据条数）
     */
    private String generateAccountingCode(String dept) {
        try {
            // 获取当天的日期字符串
            String dateStr = DateUtil.format(new Date(), "yyMMdd");

            // 查询当天已完成核算的数据条数（auditStatus = 2 表示已完成核算）
            Date today = new Date();
            Date startOfDay = DateUtil.beginOfDay(today); // 当天00:00:00
            Date endOfDay = DateUtil.endOfDay(today);     // 当天23:59:59

            LambdaQueryWrapper<BomCostOverview> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BomCostOverview::getAuditStatus, 2) // 已完成核算
                       .between(BomCostOverview::getAccountingDate, startOfDay, endOfDay); // 当天完成核算的

            long completedCount = this.count(queryWrapper);

            // 流水号为当天完成核算数据条数 + 1（因为当前这条即将完成核算）
            int sequenceNumber = (int) (completedCount + 1);

            // 生成核算代码：部门-日期+三位流水号
            String accountingCode = dept + "-" + dateStr + String.format("%03d", sequenceNumber);

            log.info("生成核算代码: dept={}, date={}, completedCount={}, sequenceNumber={}, accountingCode={}",
                dept, dateStr, completedCount, sequenceNumber, accountingCode);

            return accountingCode;

        } catch (Exception e) {
            log.error("生成核算代码失败: dept={}", dept, e);
            // 如果生成失败，使用时间戳作为备用方案
            String dateStr = DateUtil.format(new Date(), "yyMMdd");
            return dept + "-" + dateStr + "999";
        }
    }

    /**
     * 根据bomCostOverviewId+scenarioId+positiveElectrodeAccountingType获取化学元素价格
     */
    private Map<String, BigDecimal> getChemicalElementPricesByGroup(Long bomCostOverviewId, Long scenarioId, Integer accountingType) {
        Map<String, BigDecimal> pricesMap = new HashMap<>();
        try {
            if (accountingType == null) {
                log.warn("核算类型为空: bomCostOverviewId={}, scenarioId={}", bomCostOverviewId, scenarioId);
                return pricesMap;
            }

            log.info("获取化学元素价格: bomCostOverviewId={}, scenarioId={}, accountingType={}",
                bomCostOverviewId, scenarioId, accountingType);

            // 1. 根据核算类型查询对应的化学元素
            LambdaQueryWrapper<ChemicalElement> elementWrapper = new LambdaQueryWrapper<>();
            elementWrapper.eq(ChemicalElement::getAccountingType, accountingType.toString());
            List<ChemicalElement> elements = chemicalElementService.list(elementWrapper);

            if (elements.isEmpty()) {
                log.warn("未找到核算类型对应的化学元素: accountingType={}", accountingType);
                return pricesMap;
            }

            // 2. 获取化学元素ID集合
            Set<Long> elementIds = elements.stream()
                .map(ChemicalElement::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            // 3. 查询化学元素价格
            LambdaQueryWrapper<ChemicalElementPrice> priceWrapper = new LambdaQueryWrapper<>();
            priceWrapper.in(ChemicalElementPrice::getChemicalElementId, elementIds)
                       .eq(ChemicalElementPrice::getBomCostOverviewId, bomCostOverviewId)
                       .eq(ChemicalElementPrice::getScenarioId, scenarioId);

            List<ChemicalElementPrice> prices = chemicalElementPriceService.list(priceWrapper);

            // 4. 构建元素ID到名称的映射
            Map<Long, String> elementNameMap = elements.stream()
                .collect(Collectors.toMap(ChemicalElement::getId, ChemicalElement::getElementName));

            // 5. 填充价格信息
            for (ChemicalElementPrice price : prices) {
                if (price.getChemicalElementId() != null) {
                    String elementName = elementNameMap.get(price.getChemicalElementId());
                    if (elementName != null && price.getCnyPrice() != null) {
                        pricesMap.put(elementName, price.getCnyPrice());
                    }
                }
            }

            log.info("获取化学元素价格完成: bomCostOverviewId={}, scenarioId={}, accountingType={}, prices={}",
                bomCostOverviewId, scenarioId, accountingType, pricesMap);

        } catch (Exception e) {
            log.error("获取化学元素价格失败: bomCostOverviewId={}, scenarioId={}, accountingType={}",
                bomCostOverviewId, scenarioId, accountingType, e);
        }
        return pricesMap;
    }

    /**
     * 根据场景列表计算BOM总成本
     */
    private BigDecimal calculateTotalBomCostByGroup(List<Scenario> scenarios) {
        try {
            return scenarios.stream()
                .filter(scenario -> scenario.getAmountCnyWh() != null)
                .map(Scenario::getAmountCnyWh)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("计算BOM总成本失败", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 根据场景列表计算结构件成本
     */
    private BigDecimal calculateStructureCostByGroup(List<Scenario> scenarios,List<BomAccountingDetail> bomAccountingDetails) {
        try {
            // 获取所有BomAccountingDetail ID
            Set<Long> bomAccountingDetailIds = scenarios.stream()
                .map(Scenario::getBomAccountingDetailId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            if (bomAccountingDetailIds.isEmpty()) {
                return BigDecimal.ZERO;
            }

            List<BomAccountingDetail> structureDetails = bomAccountingDetails.stream()
                .filter(d -> bomAccountingDetailIds.contains(d.getId()))
                .filter(d -> d.getStructureFlag().equals(1))
                .collect(Collectors.toList());

            // 获取结构件相关的BomAccountingDetail ID
            Set<Long> structureDetailIds = structureDetails.stream()
                .map(BomAccountingDetail::getId)
                .collect(Collectors.toSet());

            // 汇总结构件的金额
            return scenarios.stream()
                .filter(scenario -> structureDetailIds.contains(scenario.getBomAccountingDetailId()))
                .filter(scenario -> scenario.getAmountCnyWh() != null)
                .map(Scenario::getAmountCnyWh)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.error("计算结构件成本失败", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算BOM总成本 - 从场景表汇总amountCnyWh
     */
    /* private BigDecimal calculateTotalBomCost(Long bomCostOverviewId, Long scenarioId) {
        try {
            // 查询该场景下的所有场景数据
            LambdaQueryWrapper<Scenario> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Scenario::getBomCostOverviewId, bomCostOverviewId)
                   .eq(Scenario::getScenarioId, scenarioId);

            List<Scenario> scenarios = scenarioService.list(wrapper);

            // 汇总金额(CNY/Wh)
            return scenarios.stream()
                .filter(scenario -> scenario.getAmountCnyWh() != null)
                .map(Scenario::getAmountCnyWh)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("计算BOM总成本失败，bomCostOverviewId: {}, scenarioId: {}", bomCostOverviewId, scenarioId, e);
            return BigDecimal.ZERO;
        }
    } */

    /**
     * 计算结构件成本 - 从场景表中筛选结构件相关的成本
     */
    /* private BigDecimal calculateStructureCost(Long bomCostOverviewId, Long scenarioId) {
        try {
            // 查询该场景下的结构件相关场景数据
            LambdaQueryWrapper<Scenario> scenarioWrapper = new LambdaQueryWrapper<>();
            scenarioWrapper.eq(Scenario::getBomCostOverviewId, bomCostOverviewId)
                          .eq(Scenario::getScenarioId, scenarioId);

            List<Scenario> scenarios = scenarioService.list(scenarioWrapper);

            // 获取结构件相关的BomAccountingDetail ID
            Set<Long> bomAccountingDetailIds = scenarios.stream()
                .map(Scenario::getBomAccountingDetailId)
                .collect(Collectors.toSet());

            if (bomAccountingDetailIds.isEmpty()) {
                return BigDecimal.ZERO;
            }

            // 查询结构件标志为1的核算明细
            LambdaQueryWrapper<BomAccountingDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.in(BomAccountingDetail::getId, bomAccountingDetailIds)
                        .eq(BomAccountingDetail::getStructureFlag, 1); // 结构件标志为1

            List<BomAccountingDetail> structureDetails = bomAccountingDetailService.list(detailWrapper);
            Set<Long> structureDetailIds = structureDetails.stream()
                .map(BomAccountingDetail::getId)
                .collect(Collectors.toSet());

            // 汇总结构件的金额
            return scenarios.stream()
                .filter(scenario -> structureDetailIds.contains(scenario.getBomAccountingDetailId()))
                .filter(scenario -> scenario.getAmountCnyWh() != null)
                .map(Scenario::getAmountCnyWh)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("计算结构件成本失败，bomCostOverviewId: {}, scenarioId: {}", bomCostOverviewId, scenarioId, e);
            return BigDecimal.ZERO;
        }
    } */

    /**
     * 获取化学元素价格 - 根据BOM成本总览的核算类型
     */
    /* private Map<String, BigDecimal> getChemicalElementPrices(Long bomCostOverviewId, Long scenarioId) {
        Map<String, BigDecimal> pricesMap = new HashMap<>();
        try {
            // 1. 先获取BOM成本总览的核算类型
            BomCostOverview bomCostOverview = this.getById(bomCostOverviewId);
            if (bomCostOverview == null || bomCostOverview.getAccountingType() == null) {
                log.warn("BOM成本总览不存在或核算类型为空: bomCostOverviewId={}", bomCostOverviewId);
                return pricesMap;
            }

            Integer accountingType = bomCostOverview.getAccountingType();
            log.info("BOM成本总览核算类型: bomCostOverviewId={}, accountingType={}", bomCostOverviewId, accountingType);

            // 2. 根据核算类型查询对应的化学元素
            // 注意：这里需要根据实际的ChemicalElement表结构来调整查询条件
            // 如果ChemicalElement的accountingType字段是String类型，需要转换
            LambdaQueryWrapper<ChemicalElement> elementWrapper = new LambdaQueryWrapper<>();
            elementWrapper.eq(ChemicalElement::getAccountingType, accountingType.toString());
            List<ChemicalElement> elements = chemicalElementService.list(elementWrapper);

            if (elements.isEmpty()) {
                log.warn("未找到核算类型对应的化学元素: accountingType={}", accountingType);
                return pricesMap;
            }

            // 3. 获取化学元素ID集合
            Set<Long> elementIds = elements.stream()
                .map(ChemicalElement::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            log.info("找到化学元素: accountingType={}, elementIds={}", accountingType, elementIds);

            // 4. 查询这些化学元素的价格
            LambdaQueryWrapper<ChemicalElementPrice> priceWrapper = new LambdaQueryWrapper<>();
            priceWrapper.in(ChemicalElementPrice::getChemicalElementId, elementIds)
                       .eq(ChemicalElementPrice::getBomCostOverviewId, bomCostOverviewId)
                       .eq(ChemicalElementPrice::getScenarioId, scenarioId);

            List<ChemicalElementPrice> prices = chemicalElementPriceService.list(priceWrapper);

            // 5. 构建元素ID到名称的映射
            Map<Long, String> elementNameMap = elements.stream()
                .collect(Collectors.toMap(ChemicalElement::getId, ChemicalElement::getElementName));

            // 6. 填充价格信息
            for (ChemicalElementPrice price : prices) {
                if (price.getChemicalElementId() != null) {
                    String elementName = elementNameMap.get(price.getChemicalElementId());
                    if (elementName != null && price.getCnyPrice() != null) {
                        pricesMap.put(elementName, price.getCnyPrice());
                    }
                }
            }

            log.info("获取化学元素价格完成: bomCostOverviewId={}, scenarioId={}, accountingType={}, prices={}",
                bomCostOverviewId, scenarioId, accountingType, pricesMap);

        } catch (Exception e) {
            log.error("获取化学元素价格失败: bomCostOverviewId={}, scenarioId={}",
                bomCostOverviewId, scenarioId, e);
        }
        return pricesMap;
    } */

    /**
     * 批量根据化学元素ID获取元素名称映射
     */
    /* private Map<Long, String> getElementNamesByIds(Set<Long> elementIds) {
        Map<Long, String> elementNameMap = new HashMap<>();

        if (elementIds == null || elementIds.isEmpty()) {
            return elementNameMap;
        }

        try {
            // 批量查询化学元素信息
            List<ChemicalElement> elements = chemicalElementService.listByIds(elementIds);

            // 构建ID到名称的映射
            for (ChemicalElement element : elements) {
                if (element.getId() != null && StrUtil.isNotBlank(element.getElementName())) {
                    elementNameMap.put(element.getId(), element.getElementName());
                }
            }

            log.debug("批量获取化学元素名称: elementIds={}, nameMap={}", elementIds, elementNameMap);

            // 如果某些ID没有找到对应的元素，记录警告
            for (Long elementId : elementIds) {
                if (!elementNameMap.containsKey(elementId)) {
                    log.warn("未找到化学元素ID: {} 对应的元素信息", elementId);
                }
            }

        } catch (Exception e) {
            log.error("批量获取化学元素名称失败: elementIds={}", elementIds, e);
            // 如果查询失败，使用硬编码的备用方案
            for (Long elementId : elementIds) {
                String elementName = getElementNameByIdHardcoded(elementId);
                if (elementName != null) {
                    elementNameMap.put(elementId, elementName);
                }
            }
        }

        return elementNameMap;
    } */

    /**
     * 硬编码方式获取元素名称（临时方案）
     * 实际应该从数据库查询
     */
    /* private String getElementNameByIdHardcoded(Long elementId) {
        if (elementId == null) {
            return null;
        }

        // 这里的ID映射需要根据实际的化学元素表数据来调整
        switch (elementId.intValue()) {
            case 1:
                return "Ni";
            case 2:
                return "Li2Co3";
            case 3:
                return "Co";
            case 4:
                return "Mn";
            case 5:
                return "Al";
            case 6:
                return "Li";
            default:
                log.warn("未知的化学元素ID: {}", elementId);
                return null;
        }
    } */

    @Override
    public void exportCompareResult(BomCostCompareExportParam param, HttpServletResponse response) {
        try {
            log.info("开始导出BOM成本对比结果");

            if (param == null) {
                log.warn("导出参数为空");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            log.info("导出参数详情: compareData size={}, materialCompareData size={}, selectedScenarios size={}",
                param.getCompareData() != null ? param.getCompareData().size() : 0,
                param.getMaterialCompareData() != null ? param.getMaterialCompareData().size() : 0,
                param.getSelectedScenarios() != null ? param.getSelectedScenarios().size() : 0);

            if (param.getCompareData() == null || param.getCompareData().isEmpty()) {
                log.warn("对比数据为空");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            // 生成文件名
            String fileName = "BOM成本对比结果_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" +
                URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20"));

            // 创建工作簿
            try (Workbook workbook = new XSSFWorkbook()) {
                // 创建对比结果工作表
                Sheet compareSheet = workbook.createSheet("BOM成本对比");
                createCompareSheet(compareSheet, param.getCompareData(), param.getSelectedScenarios());
                log.info("对比结果工作表创建完成");

                // 如果有材料对比数据，创建材料对比工作表
                if (param.getMaterialCompareData() != null && !param.getMaterialCompareData().isEmpty()) {
                    Sheet materialSheet = workbook.createSheet("材料成本对比");
                    createMaterialSheet(materialSheet, param.getMaterialCompareData(), param.getSelectedScenarios());
                    log.info("材料对比工作表创建完成");
                } else {
                    log.info("材料对比数据为空，跳过材料对比工作表创建");
                }

                // 创建化学元素价格工作表
                if (param.getCompareData() != null && !param.getCompareData().isEmpty()) {
                    Sheet chemicalSheet = workbook.createSheet("化学元素价格");
                    createChemicalPriceSheet(chemicalSheet, param.getCompareData());
                    log.info("化学元素价格工作表创建完成");
                }

                // 写入响应流
                workbook.write(response.getOutputStream());
                response.getOutputStream().flush();
                log.info("Excel文件写入响应流完成");
            }

            log.info("BOM成本对比结果导出成功");

        } catch (Exception e) {
            log.error("BOM成本对比结果导出失败", e);
            try {
                response.reset();
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

    /**
     * 创建对比结果工作表
     */
    private void createCompareSheet(Sheet sheet, List<Map<String, Object>> compareData, List<Map<String, Object>> scenarios) {
        log.info("创建对比结果工作表，数据行数: {}", compareData.size());

        if (compareData == null || compareData.isEmpty()) {
            log.warn("对比数据为空，创建空工作表");
            return;
        }

        // 创建标题行
        Row headerRow = sheet.createRow(0);

        // 第一列：产品
        Cell productCell = headerRow.createCell(0);
        productCell.setCellValue("产品");

        // 动态创建场景列
        int colIndex = 1;
        if (scenarios != null) {
            for (Map<String, Object> scenario : scenarios) {
                Cell scenarioCell = headerRow.createCell(colIndex++);
                String productName = (String) scenario.get("productName");
                String scenarioName = (String) scenario.get("scenarioName");
                scenarioCell.setCellValue(productName + "-" + scenarioName);
            }
        }

        // 添加化学元素价格列
        Cell chemicalCell = headerRow.createCell(colIndex);
        chemicalCell.setCellValue("金属前提");

        // 创建数据行
        int rowIndex = 1;
        for (Map<String, Object> rowData : compareData) {
            Row dataRow = sheet.createRow(rowIndex++);

            // 第一列：对比内容
            Cell contentCell = dataRow.createCell(0);
            Object compareContent = rowData.get("compareContent");
            contentCell.setCellValue(compareContent != null ? compareContent.toString() : "");

            // 场景数据列
            int dataColIndex = 1;
            if (scenarios != null) {
                for (Map<String, Object> scenario : scenarios) {
                    Cell dataCell = dataRow.createCell(dataColIndex++);
                    String uid = scenario.get("uid") != null ? scenario.get("uid").toString() : "";
                    Object value = rowData.get("scenario_" + uid);
                    if (value != null) {
                        dataCell.setCellValue(value.toString());
                    }
                }
            }

            // 化学元素价格列（只在第一行显示）
            if (rowIndex == 2) { // 第一个数据行
                Cell chemicalDataCell = dataRow.createCell(dataColIndex);
                Object chemicalPrices = rowData.get("chemicalPrices");
                if (chemicalPrices != null) {
                    String priceText = formatChemicalPricesForExcel(chemicalPrices);
                    chemicalDataCell.setCellValue(priceText);
                }
            }
        }

        // 自动调整列宽
        for (int i = 0; i <= colIndex; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建材料对比工作表
     */
    private void createMaterialSheet(Sheet sheet, List<Map<String, Object>> materialData, List<Map<String, Object>> scenarios) {
        log.info("创建材料对比工作表，数据行数: {}", materialData.size());

        if (materialData == null || materialData.isEmpty()) {
            log.warn("材料对比数据为空，创建空工作表");
            return;
        }

        // 创建标题行
        Row headerRow = sheet.createRow(0);

        // 第一列：项目
        Cell projectCell = headerRow.createCell(0);
        projectCell.setCellValue("项目");

        // 动态创建场景列（每个场景有两个子列）
        int colIndex = 1;
        if (scenarios != null) {
            for (Map<String, Object> scenario : scenarios) {
                String productName = (String) scenario.get("productName");
                Object scenarioId = scenario.get("id");
                String scenarioName = productName + "-场景" + scenarioId;

                // 未税单价列
                Cell priceCell = headerRow.createCell(colIndex++);
                priceCell.setCellValue(scenarioName + "-未税单价(CNY)");

                // 成本列
                Cell costCell = headerRow.createCell(colIndex++);
                costCell.setCellValue(scenarioName + "-成本(CNY/Wh)");
            }
        }

        // 创建数据行
        int rowIndex = 1;
        for (Map<String, Object> rowData : materialData) {
            Row dataRow = sheet.createRow(rowIndex++);

            // 第一列：材料类型
            Cell materialTypeCell = dataRow.createCell(0);
            Object materialType = rowData.get("materialType");
            materialTypeCell.setCellValue(materialType != null ? materialType.toString() : "");

            // 场景数据列
            int dataColIndex = 1;
            if (scenarios != null) {
                for (Map<String, Object> scenario : scenarios) {
                    String uid = scenario.get("uid") != null ? scenario.get("uid").toString() : "";

                    // 未税单价
                    Cell priceDataCell = dataRow.createCell(dataColIndex++);
                    Object priceValue = rowData.get("scenario_" + uid + "_cny");
                    if (priceValue != null) {
                        priceDataCell.setCellValue(priceValue.toString());
                    }

                    // 成本
                    Cell costDataCell = dataRow.createCell(dataColIndex++);
                    Object costValue = rowData.get("scenario_" + uid + "_cost");
                    if (costValue != null && !costValue.toString().isEmpty()) {
                        costDataCell.setCellValue(costValue.toString());
                    }
                }
            }
        }

        // 自动调整列宽
        for (int i = 0; i < colIndex; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 格式化化学元素价格数据为Excel显示文本
     */
    private String formatChemicalPricesForExcel(Object chemicalPrices) {
        if (chemicalPrices == null) {
            return "";
        }

        try {
            StringBuilder sb = new StringBuilder();

            // 处理List类型的化学元素价格数据
            if (chemicalPrices instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> priceList = (List<Map<String, Object>>) chemicalPrices;

                for (int i = 0; i < priceList.size(); i++) {
                    Map<String, Object> priceItem = priceList.get(i);
                    if (priceItem != null) {
                        String elementName = (String) priceItem.get("elementName");
                        Object cnyPrice = priceItem.get("cnyPrice");
                        String unit = (String) priceItem.get("unit");

                        if (elementName != null) {
                            sb.append(elementName).append(": ");
                            if (cnyPrice != null) {
                                // 格式化价格，保留3位小数
                                if (cnyPrice instanceof Number) {
                                    sb.append(String.format("%.3f", ((Number) cnyPrice).doubleValue()));
                                } else {
                                    sb.append(cnyPrice.toString());
                                }
                            } else {
                                sb.append("0");
                            }
                            if (unit != null) {
                                sb.append(" ").append(unit);
                            }

                            // 添加换行符（除了最后一项）
                            if (i < priceList.size() - 1) {
                                sb.append("\n");
                            }
                        }
                    }
                }
            } else {
                // 如果不是List类型，直接转换为字符串
                sb.append(chemicalPrices.toString());
            }

            return sb.toString();
        } catch (Exception e) {
            log.error("格式化化学元素价格数据失败", e);
            return "化学元素价格数据格式错误";
        }
    }

    /**
     * 创建化学元素价格工作表
     */
    private void createChemicalPriceSheet(Sheet sheet, List<Map<String, Object>> compareData) {
        log.info("创建化学元素价格工作表");

        if (compareData == null || compareData.isEmpty()) {
            log.warn("对比数据为空，无法创建化学元素价格工作表");
            return;
        }

        // 从第一行数据中获取化学元素价格
        Object chemicalPrices = null;
        for (Map<String, Object> rowData : compareData) {
            Object prices = rowData.get("chemicalPrices");
            if (prices != null) {
                chemicalPrices = prices;
                break;
            }
        }

        if (chemicalPrices == null) {
            log.warn("未找到化学元素价格数据");
            // 创建空表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("暂无化学元素价格数据");
            return;
        }

        // 创建表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("元素名称");
        headerRow.createCell(1).setCellValue("价格(CNY)");
        headerRow.createCell(2).setCellValue("单位");

        // 处理化学元素价格数据
        try {
            if (chemicalPrices instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> priceList = (List<Map<String, Object>>) chemicalPrices;

                int rowIndex = 1;
                for (Map<String, Object> priceItem : priceList) {
                    if (priceItem != null) {
                        Row dataRow = sheet.createRow(rowIndex++);

                        // 元素名称
                        String elementName = (String) priceItem.get("elementName");
                        dataRow.createCell(0).setCellValue(elementName != null ? elementName : "");

                        // 价格
                        Object cnyPrice = priceItem.get("cnyPrice");
                        if (cnyPrice != null) {
                            if (cnyPrice instanceof Number) {
                                dataRow.createCell(1).setCellValue(((Number) cnyPrice).doubleValue());
                            } else {
                                dataRow.createCell(1).setCellValue(cnyPrice.toString());
                            }
                        } else {
                            dataRow.createCell(1).setCellValue(0);
                        }

                        // 单位
                        String unit = (String) priceItem.get("unit");
                        dataRow.createCell(2).setCellValue(unit != null ? unit : "");
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理化学元素价格数据失败", e);
            Row errorRow = sheet.createRow(1);
            errorRow.createCell(0).setCellValue("数据处理失败：" + e.getMessage());
        }

        // 自动调整列宽
        for (int i = 0; i < 3; i++) {
            sheet.autoSizeColumn(i);
        }
    }
}
