@font-size: 12px;
@font-color:#333;
@border-radius:4px;
@border-color:#dee1e8;
@active-color:#1890ff;
@filter-box-height: 28px;
@filter-label-width:84px;
@filter-content-width:calc((100% - @filter-label-width) * 0.9);
@table-height: 28px;
@table-header-color:#eee;


.setup-bom-content{
    height: calc(100vh - 40px - 16px - 36px - 16px);
    font-size: @font-size;
    color: @font-color;
    overflow-y: scroll;
}

.project_details{
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 10px;
}

.project_item{
    display: flex;
    align-items: center;
    width: calc(100% / 5);
    margin-bottom: 8px;
}
.project_item .label{
    width: @filter-label-width;
    text-align: right;
    margin-right: 6px;
}

/deep/.project_details .ant-select,
/deep/.project_details .ant-input,
/deep/.project_details .ant-calendar-picker{
    font-size: @font-size;
    width: @filter-content-width;
}

/deep/.project_details .ant-select-selection--single,
/deep/.project_details .ant-input{
    height: @filter-box-height !important;
    border-radius: @border-radius;
    border: 1px solid @border-color;
}

/deep/.project_details .ant-select-selection__rendered{
    line-height: @filter-box-height;
}

/deep/.project_details .ant-select-selection--multiple{
    min-height: @filter-box-height;
    padding-bottom: 0;
    border-radius: @border-radius;
    border: 1px solid @border-color;
}

/deep/.project_details .ant-select-selection--multiple .ant-select-selection__rendered{
    margin-bottom: 0;
}

/deep/.project_details .ant-select-selection--multiple .ant-select-selection__choice{
    border-radius: @border-radius;
    padding: 0 15px 0 4px;  //为了适配最小的屏幕，避免运输方式选多个时，选择框换行
    margin-top: 2px;
}

/deep/.project_details .ant-calendar-picker input{
    width: 100%;
}

.project_item .project_field{
    border: 1px solid @border-color;
    border-radius: @border-radius;
    padding: 4.5px 11px;
    display: inline-block;
    width: @filter-content-width;
}

// 必填标志
.required-mark{
    margin-right: 2px;
    color: red;
}

.btns{
    position: absolute;
    top: -40px;
    right: 0;
}

.main {
    display: flex;
    margin-top: 10px;
}

.left_main {
    display: flex;
    overflow-x: auto;
    min-height: 120px;
    border: 1px solid @border-color;
}

/deep/.drag-tree-table{
    margin: 0;
    color: @font-color;
}

/deep/.drag-tree-table-header {
    height: @table-height !important;
    line-height: @table-height !important;
    background: @table-header-color;
    position: sticky;
    top: 0;
    z-index: 11;
}

/deep/.tree-column {
    height: @table-height;
    line-height: @table-height;
    padding: 1px  !important;
    text-align: center;
    overflow: initial;
    white-space: nowrap;
}

/deep/.readonly1 {
    color: @font-color;
    width: 100%;
    font-size: @font-size;
    padding: 0;
    border: 0;
    background: inherit;
}

/deep/.tree-column:first-child{
    text-align: left;
    margin-left: 10px;
}

/deep/.readonly {
    color: @font-color;
    width: 100%;
    font-size: @font-size;
    padding: 0;
    border: 0;
    background: inherit;
    text-align: center;
}

.left_main .borderdiv{
    justify-content: center;
}


.left_main .borderdiv input {
    color: #333;
    height: 24px;
    line-height: 24px;
    width: 60px;
    font-size: @font-size;
    outline: none;
    text-align: center;
    border: 1px solid @border-color;
    border-radius: @border-radius;
}

.right_main {
    width: 55%;
    display: block;
    transition: width 0.5s;
    overflow: auto; 
    height: calc(100vh - 40px - 16px - 36px);
    padding: 8px 12px;
    box-shadow:-2px 0 8px rgba(0,0,0,.15);
    position: absolute;
    top: -8px;
    right: -11px;
    background: #fff;
    z-index: 13;
}

.right_main_show {
    width: 0;
    margin: 0;
    padding: 0;
    border: 0;
}
.right_main_show * {
    display: none;
    
}

/deep/.right_main .ant-select-auto-complete,
/deep/.right_main .ant-select-auto-complete.ant-select .ant-input{
    height: 24px;
    font-size: @font-size;
    border-radius: @border-radius;
}

.stopdiv {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/deep/.ant-select-auto-complete.ant-select .ant-select-selection__rendered{
    line-height: 24px;
}


/deep/.right_main .ant-select-search__field__wrap,
/deep/.ant-select-combobox .ant-select-search--inline{
    height: 24px;
}

/deep/a.product_a{
    color: @active-color !important;
    display: inline-block;
}
/deep/a.product_a.disable{
    color: #8a8d8f !important;
}

.steps{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #333;
    margin: 10px 0;
}

.bom_btns{
    display: flex;
    gap:10px;
    position: absolute;
    bottom: 8px;
    right: 12px;
}


// 理伦用量 输入框样式
.numberdiv input{
    width: 100%;
    height: 24px;
    border-radius: 4px;
    border: 1px solid @border-color;
    text-align: center;
    // padding-left: 12px;
}

.numberdiv input::-webkit-outer-spin-button,
.numberdiv input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

/* 填写框...鼠标效果 */
/deep/.ant-select-selection:hover,
/deep/.ant-input:hover,
/deep/.project_field:hover,
/deep/.ant-calendar-picker:hover,
.numberdiv input:hover,
.borderdiv input:hover{
    border-color: @active-color;
}

.numberdiv input:focus-visible,
.borderdiv input:focus-visible{
    border: 1px solid @active-color !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

// 为整个页面的input 去除点击显示的黑框
input:focus-visible{
    outline: none;
}


// 子物料处的tabs
/deep/.right_main .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-nav-container{
    height: @table-height;
}
/deep/.right_main .ant-tabs .ant-tabs-small-bar .ant-tabs-nav-container{
    font-size: @font-size;
}
/deep/.right_main .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab,
/deep/.right_main .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active{
    height: @table-height;
    line-height: calc(@table-height - 2px);
    padding: 0 8px;
}


/deep/.ant-table-thead > tr > th, /deep/.ant-table-tbody > tr > td{
    padding:8px 6px;
}
/deep/.ant-table-thead > tr > th{
    border-bottom: none;
    background:@table-header-color;
}


// 下拉框文字大小
/deep/.ant-select-dropdown-menu-item{
    font-size: @font-size;
}







/deep/.ant-table {
    margin: 0 2px;
    /* margin-top: 2px; */
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    border: 0;
}

/deep/.last-column-style{
    min-width: 100px;
}

/deep/.ant-table-thead>tr>th {
    background: #efefef !important;
}

/deep/.ant-table-small>.ant-table-content>.ant-table-body {
    margin: 0;
}

/deep/.product_btn.ant-btn{
    border-radius: 3px;
    font-size: 12px;
    padding: 0 10px;
}
/deep/.product_btn.ant-btn[disabled]{
    border-color:#f5f5f5;
}
.btns .product_btn.ant-btn{
    margin-right: 8px;
}
.btns .product_btn.ant-btn:last-child{
    margin-right: 0;
}
/deep/.product_btn_primary{
    color: #fff;
    background: #1890ff;
}
/deep/.product_btn_normal{
    color: #1890ff;
    background: #f1faff;
}
/deep/.ant-table-fixed-header .ant-table-scroll .ant-table-header{
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
    overflow: initial !important;
  }
.product_dropdown .ant-dropdown-menu{
    text-align: center;
    padding: 0;
}
.product_dropdown .ant-dropdown-menu-item{
    font-size: 12px;
    padding: 2px 6px;
    border-bottom: 1px solid #e9e9e9;
    
}
.product_dropdown .ant-dropdown-menu-item > a{
    padding: 0;
    margin: 0;
    color: #000;
}

a:hover{
    color: initial;
}

.a-file{
	color: #1890ff;
	cursor: pointer;
}


.product_tabs{
    display: flex;
    align-items: center;
    padding: 10px 10px 0;
}
.product_tabs .head{
    /* color: #bbbfd1; */
    font-size: 14px;
    font-weight: bold;
    margin-right: 2px;
    margin-right: 10px;
    padding-right: 10px;
    border-right: 1px solid #1890ff;
}
.product_tabs .head.active{
    color: #1890ff;
    border-right: none;
    padding-right: 0;
}
.product_tabs .circle{
    width: 9px;
    height: 9px;
    border-radius: 55%;
    background: #1890ff;
    
}
.product_tabs .line{
    height: 1px;
    background: #1890ff;
    flex: 1;
}
.product_wrapper{
    background: #fff;
    border-radius: 5px;
    margin: 10px 10px 0;
    overflow: hidden;
}
.close_bom{
    cursor: pointer;
}

// select {
//     color: rgba(0, 0, 0, 0.65);
//     display: block;
//     height: 23px;
//     line-height: 23px;
//     font-size: 14px;
//     outline: none;
//     border: 1px solid #d9d9d9;
// }



p.error {
    color: #eb3f75;
    margin-bottom: 2px;
}

.uncheck {
    color: #a7a5a5;
}

/deep/.vue-treeselect--has-value .vue-treeselect__multi-value {
    margin: 0;
}





.text-align {
    text-align: center;
}
/deep/.tree-row {
    line-height: initial !important;
}





.moveTip {
    color: #fff;
    background: #043f81cf;
    min-height: 20px;
    min-width: 60px;
    position: fixed;
    border-radius: 2px;
    z-index: 1000;
    padding: 5px;
}









/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item {
    margin-bottom: 14px;
}
/deep/.table-page-search-wrapper .ant-form-inline .ant-form-item .ant-form-item-control {
    height: auto;
}
/deep/.ant-drawer-body {
    height: 100%;
}
/deep/.ant-row{
    margin-left: 0 !important;
    margin-right: 0 !important;
}
.arrow_btn{
    /* position: absolute;
    top: -10px; */
    cursor: pointer;
    z-index: 12;
    display: flex;
    align-items: center;
}
/* .arrow_btn.left{
    right: 0;
}
.arrow_btn.right{
    right: 50%;
} */
.arrow_btn.left::after{
    content: '\00AB';
    font-size: 25px;
    font-weight: bold;
}
.arrow_btn.right::after{
    content: '\00BB';
    font-size: 25px;
    font-weight: bold;
}

.form_row{
    display: flex;
    margin-bottom: 12px;
}

.form_row .form_item{
    margin: 0 4px;
    display: flex;
    flex: 1;
    align-items: center;
}

/deep/ .form_row .form_item .ant-input{
    height: @table-height;
    font-size: @font-size;
}

/deep/.ant-modal-footer{
    padding: 0 24px 24px;
}

// /deep/.ant-modal-body,/deep/.ant-modal-header,/deep/.ant-modal-footer{
    
//     padding: 8px;
// }
// /deep/.ant-modal-header{
//     border-bottom: 1px solid #1890ff;
// }
/deep/.ant-modal-title{
    font-size: 14px;
}
/deep/.ant-modal-close-x{
    width: 50px;
    height: 50px;
    font-size: 14px;
}
/deep/.ant-form label{
    font-size: 12px;
}
/deep/.ant-form-item{
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}
/deep/.ant-select-selection-placeholder,
/deep/.ant-input::placeholder,
/deep/.vue-treeselect__placeholder{
    font-size: @font-size;
}
// /deep/.ant-modal-close-icon{
//     border: 1px solid #1890ff;
//     padding: 2px;
//     color: #1890ff;
// }


/deep/.ant-btn{
    border-radius: @border-radius;
    font-size: @font-size;
}
/deep/.ant-btn-primary{
    color: #fff !important;
    background: #1890ff !important;
}
/deep/.s-table-tool{
    padding: 0;
}

/deep/.endBomTable.table-wrapper{
    margin-top: 25px;
}
/deep/.step_spin .ant-table{
    line-height: 1.0;
}
/deep/.ant-table-pagination.ant-pagination{
    display: flex;
}


.steps .seq{
    width: 17px;
    height: 17px;
    border-radius: 50%;
    border: 1px solid ;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #efefef;
    border-color: #bbbbbb;
    margin-right: 5px;
    font-size: 13px;
}

.steps .seq.active{
    background:#1890ff;
    border-color: #1890ff;
    color: #fff;
}

.steps .line{
    height: 1px;
    width: 80px;
    background:#1890ff;
    margin: 0 15px;
    
}

.params_btns{
    padding: 10px 0;
}


/deep/.step_spin.ant-spin-nested-loading{
    overflow-x: auto;
}
/deep/.partLoss{
    display: flex;
    position: relative;
    justify-content: center;
}


/deep/.partLoss .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #043f81cf;
    color: #fff;
    font-size: 12px;
    text-align: left;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    top: 75%;
    left: 50%;
    margin-left: -110px;
    border-radius: 5px;
    padding: 5px 8px;
}
/deep/.partLoss .tooltiptext p{
    margin-bottom: 5px;
}
/deep/.partLoss .tooltiptext::after {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent #043f81cf transparent;
}

/deep/.partLoss:hover .tooltiptext {
    visibility: visible;
}


.form_tip{
    position: relative;
    margin-left: 8px;
    margin-top: 3px;
}

.form_tip .tooltiptext {
    visibility: hidden;
    width: 100px;
    background-color: #043f81cf;
    color: #fff;
    font-size: 12px;
    text-align: left;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 444;
    top: 75%;
    left: 50%;
    margin-left: -50px;
    border-radius: 5px;
    padding: 5px 8px;
    text-align: center;
}
.form_tip .tooltiptext p{
    margin-bottom: 5px;
}
.form_tip .tooltiptext::after {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent #043f81cf transparent;
}

.form_tip:hover .tooltiptext {
    visibility: visible;
}
/deep/.ant-pagination-options{
    display: flex;
    align-items: center;
}

/deep/.text_ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: relative;
}

/deep/.text_ellipsis .tooltiptext {
    visibility: hidden;
    width: 100px;
    background-color: #ffffffcf;
    color: #000;
    font-size: 12px;
    text-align: left;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    top: 75%;
    left: 50%;
    margin-left: -50px;
    border-radius: 5px;
    padding: 5px 8px;
    text-align: center;
}
/deep/.space{
    width: 3px;
}
/deep/.text_ellipsis .tooltiptext::after {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent #fff transparent;
}

/deep/.text_ellipsis:hover .tooltiptext {
    visibility: visible;
}

/deep/.tree-row.highlight {
    background: #e1ecf7 !important;
}
/deep/ .part_table .ant-table-thead tr:nth-child(1) th:nth-child(7){
    position: sticky;
    right: 50px;
    z-index: 99;
    box-shadow: -6px 0 6px -4px rgba(0,0,0,.15);
    
}
/deep/ .part_table .ant-table-thead tr:nth-child(1) th:nth-child(8){
    position: sticky;
    right: 0;
    z-index: 99;
}
/deep/.part_table .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td:nth-child(7){
    background: #e6f7ff;
}
/deep/ .part_table .ant-table-tbody tr td:nth-child(7){
    position: sticky;
    right: 50px;
    z-index: 99;
    box-shadow: -6px 0 6px -4px rgba(0,0,0,.15);
    background: #fff;
}
/deep/ .part_table .ant-table-tbody tr td:nth-child(8){
    position: sticky;
    right: 0;
    z-index: 99;
}
/deep/.ant-table-body{
    overflow-y: auto;
}
/* 10082587 10082587 10082587 10082587 10082587 10082587 10082587 10082587 10082587 10082587 10082587 10082587 10082587 */
/deep/.part_table .ant-table-thead{
    position: sticky;
    top: 0;
    z-index: 100;
}

/deep/.vue-treeselect__multi-value-item {
	/* background: transparent; */
	font-size: 12px;
	vertical-align: initial;
}
/deep/.vue-treeselect__placeholder, .vue-treeselect__single-value{
    line-height: 30px;
}
// /deep/.vue-treeselect__control {
	/* display: flex;
	align-items: center;
	justify-content: center;
	height: 24px;
	overflow: hidden; */
	// border-radius: initial;
    // border: 1px solid #1890ff;
// }
/* /deep/.vue-treeselect__control * {
	padding: 0 !important;
	margin: 0 !important;
	line-height: initial !important;
	white-space: nowrap;
} */
/* /deep/.vue-treeselect__limit-tip-text {
	margin-top: 2px !important;
} */
/deep/.vue-treeselect__multi-value-item-container{
    padding: 2px !important;
}
/deep/.left_main .tree-column.border{
    border-right: 1px solid transparent;
}

// 分页大小
/deep/.ant-pagination-disabled,
.ant-pagination-next {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

/deep/.ant-pagination-prev {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

/deep/.ant-pagination-next {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

/deep/.ant-pagination-jump-prev {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

/deep/.ant-pagination-jump-next {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}

/deep/.ant-pagination-item {
  min-width: 25px;
  height: 25px;
  line-height: 25px;
}
/deep/.ant-table-pagination.ant-pagination{
    font-size: 12px;
  }

  .searchForm{
    display: flex;
    justify-content: space-between;
    padding: 15px 10px;
  }

  .searchForm .left, .searchForm .right{
    display: flex;
    align-items: center;
  }

  .searchForm .left{
    width: 78%;
  }

  .searchForm .searchItem{
    display: flex;
    align-items: center;
    flex: 1;
  }

  .searchForm .searchItem span{
    margin-right: 10px;
    font-size: 12px;
    color: #000;
  }

  .searchForm .vue-treeselect{
    flex: 1;
    margin-right: 10px;
  }

  /deep/.searchItem .ant-calendar-picker{
    flex: 1;
    
  }


  /deep/.searchForm .vue-treeselect__control{
    height: 28px !important;
    line-height: 20px !important;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }
  /deep/.searchForm .vue-treeselect__placeholder, .vue-treeselect__single-value{
    line-height: 28px !important;
    font-size: 12px;
  }
  /deep/.searchForm  .vue-treeselect__control * {
    padding: 0 !important;
    margin: 0 !important;
    white-space: nowrap;
}

/deep/.vue-treeselect__multi-value-item-container{
    display: inline-flex;
}
/deep/.ant-table-middle > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr > td,
/deep/.ant-table-middle > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-tbody > tr > td{
    padding: 10px 2px;
}
/deep/.searchForm .ant-input{
    font-size: 12px;
    height: 28px;
    line-height: 28px;
}
