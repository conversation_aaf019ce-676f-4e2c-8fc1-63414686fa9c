<template>
    <div>
        <blockTabsIndex
            :pageLevel="1" 
            :pageTitleShow="false" 
            :tabsList="tabsList"  
            :activeKey="activeKey" 
            :tabsFontSize="13"
            @tabClick="callback"  
            @onEdit="onEdit" 
            :tableTotal="tableTotal"  
            :loading="loading"  
            @paginationChange="handlePageChange"
            @paginationSizeChange="handlePageChange"
        >
            <!-- 表格 -->
            <template #table>
                <ag-grid-vue
                    v-if="activeKey != '1'"
                    :style='`height: ${tableHeight}px`' 
                    :tooltipShowDelay="0"
                    class="table ag-theme-balham"
                    :columnDefs='columnDefs'
                    :rowData='dataSource' 
                    :grid-options="gridOptions"
                    :suppressDragLeaveHidesColumns="true"
                    :suppressMoveWhenColumnDragging="true"
                    
                >
                </ag-grid-vue>
                <endboms @closeBom="closeBom" v-else :issueId="issueId" :bom="bom" :bomType="parseInt(bomType)" />
            </template>
        </blockTabsIndex>

        <bomextendverify ref="bomextendverify" />
        <bomhistory ref="bomhistory" />
        <checkhistory2 ref="checkhistory2" @endBomPreview="endBomPreview" />

        <a-modal :title="addWerkTitle" :width="600" :visible="lineModalVisible" @ok="bomEndSetLines" @cancel="lineCancel">
            <div>
                <p style="margin:0">请选择成品BOM适用工厂 ({{endBomVersoin}}):</p>
                <a-checkbox-group v-model="checkedList" :options="options" />
            </div>
            <div>
                <p style="margin:0;margin-top:10px">包装BOM适用工厂:</p>
                <span v-for="(item,i) in packLineIds" :key="i">{{dataLines[item]}}&nbsp;&nbsp;&nbsp;&nbsp;</span>
            </div>
        </a-modal>


        <a-modal title="删除工厂" :width="600" :visible="dellineModalVisible" @ok="bomEndDelLines" @cancel="delLineCancel">
            <div>
                <p style="margin:0">请选择成品BOM适用工厂 ({{endBomVersoin}}):</p>
                <a-checkbox-group v-model="delcheckedList" :options="deloptions" />
            </div>
        </a-modal>


        <a-modal title="输入sap版本" :width="200" :visible="sapvisible" @ok="sapImportBomEnd" @cancel="sapcancel">
            <div>
                <a-input :style="{width:'100%'}" v-model="sapverison" />
            </div>
        </a-modal>

        <a-modal title="失败原因" :width="400" :visible="errorsVisible" @cancel="errorCancel">
            <template slot="footer">
                <div></div>
            </template>
            <p>{{errors}}</p>
        </a-modal>

        <a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="pdfVisible"
            @close="pdfVisible = false">
            <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%"></iframe>
        </a-drawer>

    </div>
</template>

<script>
    import Vue from 'vue'
    import endboms from './endboms'
    import bomextendverify from './bomextendverify'
    import bomhistory from './bomhistory'
    import checkhistory2 from './checkhistory2'
    import {
        getBomEndPage,
        getwerklines,
        endBomPreview,
        bomEndRelate,
        getBomPackLineIds,
        bomEndSetLines,
        getBomLineIds,
        bomEndDelLines,
        getBomEndError,
        delBomEnd,
        sapImportBomEnd,
        sysBomEndWidthDraw,
        pdfUpdate
    } from "@/api/modular/system/bomManage"
    export default {
        props: {
            issueId: {
                type: Number,
                default: 0
            },
            projectdetail: {
                type: Object,
                default: {}
            },
        },
        components: {
            endboms,
            bomextendverify,
            bomhistory,
            checkhistory2,
            cellBomCodeCellRender:{
                template: `
                    <a @click="params.onOpen(params.data.bomId)">{{params.value}}</a>
                `
            },
            packBomCodeCellRender:{
                template: `
                    <a @click="params.onOpen(params.data.bomPackId)">{{params.value}}</a>
                `
            },
            bomVersionCellRender:{
                template: `
                    <a @click="params.onOpen(params.data.id)">{{params.value}}</a>
                `
            },

            linesCellRender:{
                template: `
                <div>
                    <div v-if="params.value != '[]'">
                        {{params.onLinesRenderer(params)}}
                    </div>
                    <div v-else></div>
                </div>`
            },

            checkHistoryCellRender:{
                template: `
                <span>
                    <a v-if="hasPerm('sysBomPush:list')" @click="params.onOpen(params)">
                        查阅
                    </a>
                    <span v-else>
                        查阅
                    </span>
                </span>`
            },

            historyCellRender:{
                template: `
                <span>
                    <a v-if="hasPerm('sysBom:history')" @click="params.onOpen(params)">
                        查阅
                    </a>
                    <span v-else>
                        查阅
                    </span>
                </span>`
            },

            werkCellRender:{
                template: `
                    <div>
                        <template v-if="(!params.data.isCheck && params.data.bomRelateStatus == 0) || params.data.bomRelateStatus == 4 || params.data.bomRelateStatus == 2 || params.data.bomRelateStatus == 7">
                            <a-dropdown placement="bottomCenter" overlayClassName='product_dropdown'>
                                <a-button style="font-size:12px" type="link" size='small'> 工厂维护 <a-icon type="down" /></a-button>
                                <a-menu slot="overlay">
                                    <a-menu-item v-if="hasPerm('sysBomLine:addlines')">
                                        <a @click="params.onOpen(params)" style="font-size: 12px;text-align:center">
                                            {{params.data.bomLines != '[]' ? '新增' : '设置'}}工厂
                                        </a>
                                    </a-menu-item>
                                    <a-menu-item v-if="hasPerm('sysBomLine:deletelines') ">
                                        <a v-if="JSON.parse(params.data.bomLines).length > 1> 1 || !params.data.isCheck" @click="params.onDelWerk(params)" style="font-size: 12px;text-align:center">删除工厂</a>
                                        <a v-else :style="{cursor:'not-allowed'}" style="font-size: 12px;text-align:center">删除工厂</a>
                                    </a-menu-item>

                                </a-menu>
                            </a-dropdown>
                        </template>
                        <a-button style="font-size:12px;text-align:center" v-else type="link" size='small' disabled> 工厂维护 <a-icon type="down" /></a-button>
                    </div>
                `
            },

            actionRender:{
                template: `
                <div>
                    <a-dropdown placement="bottomCenter" overlayClassName='product_dropdown'>
                        <a-button style="font-size:12px" type="link" size='small'> 更多 <a-icon type="down" /></a-button>
                        <a-menu v-if="params.data.bomRelateStatus == 3" slot="overlay">
                            <a-menu-item>
                                <a @click="params.onErrorView(params)" style="font-size: 12px;text-align:center">查看</a>
                            </a-menu-item>
                        </a-menu>
                        <a-menu v-if="params.data.bomRelateStatus == 0 || params.data.bomRelateStatus == 4 || params.data.bomRelateStatus == 7" slot="overlay">
                            <a-menu-item v-if="hasPerm('sysBom:save') && params.data.bomCode">
                                <a @click="params.onEdit(params)" style="font-size: 12px;text-align:center">编辑</a>
                            </a-menu-item>
                            <a-menu-item v-if="hasPerm('sysBom:commit')">
                                <a @click="params.onOa(params)" style="font-size: 12px;text-align:center">提交</a>
                            </a-menu-item>
                            <a-menu-item v-if="hasPerm('sysBom:save')">
                                <a @click="params.onWithdraw(params)" style="font-size: 12px;text-align:center">撤回</a>
                            </a-menu-item>
                            <a-menu-item v-if="hasPerm('sysBom:save') && (params.data.bomRelateStatus == 0 || params.data.bomRelateStatus == 4) && !params.data.isCheck">
                                <a @click="params.onDelBom(params)" style="font-size: 12px;text-align:center">删除</a>
                            </a-menu-item>
                            <a-menu-item>
                                <a @click="params.onSap(params)" style="font-size: 12px;text-align:center">从sap导入</a>
                            </a-menu-item>
                        </a-menu>

                        <a-menu v-if="params.data.bomRelateStatus == 2" slot="overlay">
                            <a-menu-item v-if="hasPerm('sysBom:get')">
                                <a @click="params.onShowConfirm(params)" style="font-size: 12px;text-align:center">修订</a>
                            </a-menu-item>
                            
                        </a-menu>
                        <a-menu v-if="params.data.bomRelateStatus == 5 || params.data.bomRelateStatus == 6" slot="overlay">
                            
                            <a-menu-item v-if="hasPerm('sysBom:commit')">
                                <a @click="params.onOa(params)" style="font-size: 12px;text-align:center">提交</a>
                            </a-menu-item>
                        </a-menu> 
                        
                    </a-dropdown>
                </div>
                `
            }
        },
        data() {
            return {
                btnBom:{
                    '0': '真电芯',
                    '2': '配重电芯',
                    '3': '空壳电芯'
                },
                tableTotal: 0,
                pageNo:1,
                pagesize: 20,
                baseApiUrl: '/api/sysFileInfo/preview?Authorization=Bearer ',
                activeKey:'0',
                bomType: '0',
                loading: false,
                dataSource: [],
                bom: {},
                tableHeight: document.documentElement.clientHeight - 40 - 32 - 36 - 48 - 22,
                dataLines: {},
                dataSource: [],
                tabsList: [
                    {
                        value: '0',
                        label: '真电芯成品BOM',
                        closable: false
                    },
                    {
                        value: '2',
                        label: '配重电芯成品BOM',
                        closable: false
                    },
                    {
                        value: '3',
                        label: '空壳电芯成品BOM',
                        closable: false
                    },
                    {
                        value: '1',
                        label: '成品BOM修订',
                        closable: true,
                        show: false
                    },
                ],
                columnDefs:[
                    {   
                        headerName: '序号', 
                        field: 'no' , 
                        width: 110,
                        cellRenderer: function (params) {
                            return parseInt(params.node.id) + 1
                        },
                    },
                    {   
                        headerName: '产品名称', 
                        field: 'producttName', 
                        width: 110,
                        cellRenderer: this.producttNameCellRenderer,
                    },
                    {   
                        headerName: '成品物料代码', 
                        field: 'bomCode', 
                        width: 110
                    },
                    {   
                        headerName: '文件编号', 
                        field: 'bomVersion',
                        minWidth:260,
                        cellRenderer: 'bomVersionCellRender',
                        cellRendererParams: { onOpen: this.endBomPreview },
                    },
                    {   
                        headerName: '适用工厂', 
                        field: 'bomLines', 
                        cellRenderer: 'linesCellRender',
                        cellRendererParams: { onLinesRenderer: this.linesRenderer },
                        tooltipValueGetter: (p) =>  this.linesRenderer(p), 
                    },
                    {   
                        headerName: '流程审批', 
                        field: 'checkhistory', 
                        width: 100,
                        cellRenderer: 'checkHistoryCellRender',
                        cellRendererParams: { onOpen: this.checkHistoryRenderer },
                    },
                    {   
                        headerName: '变更履历', 
                        field: 'history', 
                        width: 100,
                        cellRenderer: 'historyCellRender',
                        cellRendererParams: { onOpen: this.historyRenderer },
                    },
                    {   
                        headerName: '操作', 
                        field: 'action', 
                        width: 110,
                        cellRenderer: 'actionRender',
                        cellRendererParams: { 
                            onErrorView: this.getBomEndError,
                            onEdit: this.editBom,
                            onOa:this.bomEndRelate,
                            onWithdraw:this.sysBomEndWidthDraw,
                            onDelBom:this.delBomEnd,
                            onSap: this.sapshow,
                            onShowConfirm: this.showConfirm
                        },
                    },
                    {   
                        headerName: '工厂维护', 
                        field: 'werk', 
                        width: 120,
                        cellRenderer: 'werkCellRender',
                        cellRendererParams: {  onOpen: this.showLine, onDelWerk: this.showDelLine },
                    },
                    {   
                        headerName: '电芯物料代码', 
                        field: 'cellBomCode', 
                        width: 96,
                        cellRenderer: 'cellBomCodeCellRender',
                        cellRendererParams: { onOpen: this.pdfUpdate}
                    },
                    {   
                        headerName: '包装物料代码', 
                        field: 'packBomCode', 
                        width: 96,
                        cellRenderer: 'packBomCodeCellRender',
                        cellRendererParams: { onOpen: this.pdfUpdate}
                    },
                ],

                lineflag: 0,
                packLineIds: [],
                endBomVersoin: '',

                id: 0,
                lineModalVisible: false,
                addWerkTitle: '',
                dellineModalVisible: false,

                deloptions: [],
                options: [],

                sapverison : '',
                sapvisible: false,

                checkedList: [],
                delcheckedList:[],

                errorsVisible: false,
                errors: '',

                pdfUrl: '',
                pdfVisible: false,
                gridOptions:{
                    onGridReady: (event) => {
                        event.api.sizeColumnsToFit();
                    }
                }
            }
        },
        methods: {
            handlePageChange(value){
                let { current, pageSize } = value
                this.pageNo = current
                this.pageSize = pageSize
                this.getBomEndPage()
            },
            historyRenderer(params){
                this.$refs.bomhistory.edit(10,this.projectdetail,params.data)
            },
            checkHistoryRenderer(params){
                this.$refs.checkhistory2.edit(10,params.data)
            },
            linesRenderer(params){
                const lines = JSON.parse(params.value)
                return lines.map(item => this.dataLines[item]).join(';')
            },
            previewPdf(id) {
                this.pdfUrl = this.baseApiUrl + Vue.ls.get('Access-Token')+'&id=' + id
                this.pdfVisible = true
			},
            pdfUpdate(id) {
                this.loading = true
                pdfUpdate({
                    id:id
                }).then((res) => {
                    if (res.success) {
                        this.pdfUrl = this.baseApiUrl + Vue.ls.get('Access-Token')+'&id=' + res.data
                        this.pdfVisible = true
                    } 
                }).finally(() => {
                    this.loading = false
                })
            },

            endBomPreview(id) {
                this.loading = true
                endBomPreview({
                    id: id
                }).then((res) => {
                    if (res.success) {
                        this.pdfUrl = this.baseApiUrl + Vue.ls.get('Access-Token')+'&id=' + res.data.fileId
                        this.pdfVisible = true
                    } 
                }).finally(() => {
                    this.loading = false
                })
            },

            errorCancel(){
                this.errors = ''
                this.errorsVisible = false
            },
            getBomEndError(params) {
                this.loading = true
                getBomEndError({
                    id: params.data.id
                })
                .then((res) => {
                    if (res.success) {
                        this.errors = res.data.erros
                        this.errorsVisible = true
                    }
                }).finally(()=>{
                    this.loading = false
                })
            },

            sapshow(params) {
                this.id = params.data.id
                this.sapvisible = true
            },
            sapcancel() {
                this.id = 0
                this.sapverison = ''
                this.sapvisible = false
            },
            sapImportBomEnd() {
                this.loading = true
                sapImportBomEnd({
                    id: this.id,
                    sapVersion: this.sapverison
                })
                .then((res) => {
                    if (res.success) {
                        this.$message.info('已导入', 1);
                        this.sapcancel()
                    } 
                }).finally(() => {
                    this.loading = false
                })
            },

            showConfirm(params) {
                let that = this
                this.$confirm({
                    title: '请确认是否修订?',
                    onOk() {
                        that.editBom(params)
                    },
                    onCancel() {
                    },
                    class: 'test',
                });
            },


            showDelLine(params) {
                this.id = params.data.id
                this.dellineModalVisible = true
                this.getBomLineIds(params)
            },
            delLineCancel() {
                this.id = 0
                this.delcheckedList = []
                this.deloptions = []
                this.dellineModalVisible = false
            },
            getBomLineIds(params) {
                getBomLineIds({
                    id: params.data.id
                })
                .then((res) => {
                    if (res.success) {
                        this.lineflag = res.data.lineflag
                        this.endBomVersoin = res.data.endBomVersoin
                        for (const _item of res.data.bomlines) {
                            this.deloptions.push({
                                label: this.dataLines[_item],
                                value: _item
                            })
                        }
                    }
                }).finally(() => {
                    this.loading = false
                })
            },
            bomEndDelLines() {
                this.loading = true
                bomEndDelLines({
                    id: this.id,
                    bomLines: JSON.stringify(this.delcheckedList)
                }).then((res) => {
                    if (res.success) {
                        this.getBomEndPage()
                        this.delcancel()
                    }
                }).finally(() => {
                    this.loading = false
                })
            },

            showLine(params) {
                this.id = params.data.id
                this.lineModalVisible = true
                this.addWerkTitle = params.data.bomLines == '[]' ? '设置工厂' : '添加工厂'
                this.getBomPackLineIds(params)
            },
            lineCancel() {
                this.id = 0
                this.options = []
                this.checkedList = []
                this.lineModalVisible = false
            },
            getBomPackLineIds(params) {
                this.loading = true
                getBomPackLineIds({
                    id: params.data.id
                })
                .then((res) => {
                    if (res.success) {
                        this.lineflag = res.data.lineflag
                        this.packLineIds = res.data.packlines
                        this.endBomVersoin = res.data.endBomVersoin
                        for (const _item of res.data.bomlines) {
                            this.options.push({
                                label: this.dataLines[_item],
                                value: _item
                            })
                        }
                    }
                }).finally(() => {
                    this.loading = false
                })
            },
            bomEndSetLines() {
                if (this.checkedList.length <= 0) {
                    this.$message.error('请选择产线')
                    return false
                }
                this.loading = true
                bomEndSetLines({
                    id: this.id,
                    bomLines: JSON.stringify(this.checkedList)
                })
                .then((res) => {
                    if (res.success) {
                        this.getBomEndPage()
                        this.cancel()
                    }
                }).finally(() => {
                    this.loading = false
                })
            },

            bomEndRelate(params) {
                this.loading = true
                bomEndRelate({
                    id: params.data.id
                }).then((res) => {
                    if (res.success) {
                        this.$message.success('关联成功')
                    } else {
                        if (res.code == 501) {
							this.$refs.bomextendverify.view(res.data)
						}
                        this.$message.error('关联失败：' + res.message)
                    }
                    this.getBomEndPage()

                }).finally((res) => {
                    this.loading = false
                })
            },
            getwerklines() {
                this.loading = true
                getwerklines({werkType: 1}).then((res) => {
                    if (res.success) {
                        let mapline = {}
                        for (const key in res.data) {
                            for (const _item of res.data[key]) {
                                mapline[_item.id] = _item.werkNo + '->' + _item.lineName
                            }
                        }
                        this.dataLines = mapline
                    }
                }).finally(()=>{
                    this.loading = false
                })
            },
            getBomEndPage(){
                this.loading = true
                getBomEndPage({...{pageNo:this.pageNo,pageSize:this.pageSize},bomIssueId : this.issueId, bomType: this.bomType}).then((res) => {
                    if (res.success) {
                        this.dataSource = res.data.rows
                        this.tableTotal = res.data.totalRows
                    }
                }).finally(()=>{
                    this.loading = false
                })
            },
            producttNameCellRenderer (params) {
                return this.projectdetail.productProjectName
            },
            closeBom() {

            },
            callback(val) {
                this.activeKey = val
                if (val != '1'){
                    this.bomType = val
                    this.getBomEndPage()
                }
            },
            onEdit(key) {
                if (key != '1') {
                    return false;
                }
                let show = this.tabsList.find(item => item.value == key).show 
                show = !show
                this.tabsList.find(item => item.value == key).show = show
                if(!show){
                    this.activeKey = this.bomType
                }else{
                    this.activeKey = key
                }
            },
            delBomEnd(params) {
                this.loading = true
                delBomEnd({
                    id: params.data.id
                }).then((res) => {
                    if (res.success) {
                        this.$message.success('删除成功')
                        this.getBomEndPage()
                    }
                }).finally((res) => {
                    this.loading = false
                })
            },
            editBom(params) {
                this.activeKey = '1'
                this.bom = params.data
                this.tabsList.find(item => item.value == this.activeKey).label = `${this.btnBom[this.bomType]}成品BOM修订`
                this.tabsList.find(item => item.value == this.activeKey).show = true
            },
            closeBom(){
                this.activeKey = this.bomType
                this.bom = {}
                this.tabsList.find(item => item.value == '1').show = false
            },
            sysBomEndWidthDraw(params){
                this.loading = true
                sysBomEndWidthDraw({
                    id : params.data.id
                }).then((res) => {
                    if (res.success) {
                        this.$message.info('已撤回', 1);
                        this.getBomEndPage()
                    }
                }).finally((res) => {
                    this.loading = false
                })
            },
        },
        created() {
            this.getwerklines()
            this.getBomEndPage()
        }
    }
</script>
<style lang='less' scoped=''>
    ::v-deep .ant-tabs-bar{
        margin:0
    }
    .product_dropdown {
        .ant-dropdown-menu{
            text-align: center;
            padding: 0;
        }
        .ant-dropdown-menu-item{
            font-size: 12px;
            padding: 2px 6px;
            border-bottom: 1px solid #e9e9e9;
        }
        .ant-dropdown-menu-item > a{
            padding: 0;
            margin: 0;
            color: #000;
        }
    }
    .page-container{
        height: calc(100vh - 40px - 32px - 16px);
        margin:8px 10px;
    }
</style>