package eve.sys.modular.bombill.positivematerialaccounting.controller;

import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.modular.bombill.positivematerialaccounting.dto.PositiveMaterialAccountingExcelDto;
import eve.sys.modular.bombill.positivematerialaccounting.entity.PositiveMaterialAccounting;
import eve.sys.modular.bombill.positivematerialaccounting.param.PositiveMaterialAccountingCheckDuplicateParam;
import eve.sys.modular.bombill.positivematerialaccounting.param.PositiveMaterialImportParam;
import eve.sys.modular.bombill.positivematerialaccounting.service.IPositiveMaterialAccountingService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/positiveMaterialAccounting")
public class PositiveMaterialAccountingController {

    @Resource
    private IPositiveMaterialAccountingService positiveMaterialAccountingService;

    @PostMapping("/pageList")
    @BusinessLog(title = "正极材料核算-分页列表查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData pageList(@RequestBody PositiveMaterialAccounting param) {
        return new SuccessResponseData(positiveMaterialAccountingService.pageList(param));
    }

    @PostMapping("/list")
    @BusinessLog(title = "正极材料核算-列表查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData list(@RequestBody PositiveMaterialAccounting param) {
        return new SuccessResponseData(positiveMaterialAccountingService.list(param));
    }

    @PostMapping("/get")
    @BusinessLog(title = "正极材料核算-根据id查询", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData get(@RequestBody PositiveMaterialAccounting param) {
        return new SuccessResponseData(positiveMaterialAccountingService.get(param));
    }

    @PostMapping("/add")
    @BusinessLog(title = "正极材料核算-新增", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData add(@RequestBody PositiveMaterialAccounting param) {
        return new SuccessResponseData(positiveMaterialAccountingService.add(param));
    }

    @PostMapping("/delete")
    @BusinessLog(title = "正极材料核算-删除", opType = LogAnnotionOpTypeEnum.DELETE)
    public ResponseData delete(@RequestBody PositiveMaterialAccounting param) {
        return new SuccessResponseData(positiveMaterialAccountingService.delete(param));
    }

    @PostMapping("/update")
    @BusinessLog(title = "正极材料核算-更新", opType = LogAnnotionOpTypeEnum.UPDATE)
    public ResponseData update(@RequestBody PositiveMaterialAccounting param) {
        return new SuccessResponseData(positiveMaterialAccountingService.update(param));
    }

    /**
     * 下载导入模板
     *
     * @param response HTTP响应
     */
    @PostMapping("/downloadTemplate")
    @BusinessLog(title = "正极材料核算-下载导入模板", opType = LogAnnotionOpTypeEnum.EXPORT)
    public void downloadTemplate(HttpServletResponse response) {
        positiveMaterialAccountingService.downloadTemplate(response);
    }

    /**
     * 导入Excel
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/importExcel")
    @BusinessLog(title = "正极材料核算-导入Excel", opType = LogAnnotionOpTypeEnum.IMPORT)
    public ResponseData importExcel(@RequestParam("file") MultipartFile file) {
        List<PositiveMaterialAccountingExcelDto> result = positiveMaterialAccountingService.importExcel(file);
        return new SuccessResponseData(result);
    }

    /**
     * 批量保存导入的数据
     *
     * @param dataList 数据列表
     * @return 保存结果
     */
    @PostMapping("/batchSaveImportData")
    @BusinessLog(title = "正极材料核算-批量保存导入数据", opType = LogAnnotionOpTypeEnum.ADD)
    public ResponseData batchSaveImportData(@RequestBody PositiveMaterialImportParam /* List<PositiveMaterialAccountingExcelDto> */ param) {
        Boolean result = positiveMaterialAccountingService.batchSaveImportData(param);
        return new SuccessResponseData(result);
    }

    /**
     * 检查重复的正极体系编号
     *
     * @param param 检查参数，包含正极体系编号列表
     * @return 重复的正极体系编号列表
     */
    @PostMapping("/checkDuplicate")
    @BusinessLog(title = "正极材料核算-检查重复正极体系编号", opType = LogAnnotionOpTypeEnum.QUERY)
    public ResponseData checkDuplicate(@RequestBody PositiveMaterialAccountingCheckDuplicateParam param) {
        List<String> duplicateSystemCodes = positiveMaterialAccountingService.checkDuplicateSystemCodes(param);
        return new SuccessResponseData(duplicateSystemCodes);
    }
}