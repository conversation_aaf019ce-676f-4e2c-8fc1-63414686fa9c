package eve.sys.modular.bom.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.util.StrUtil;
import eve.core.exception.ServiceException;
import eve.sys.modular.bom.entity.SysBom;
import eve.sys.modular.bom.params.BomExtendReq;
import eve.sys.modular.bom.params.BomExtendResp;
import eve.sys.modular.bom.params.BomSapProof;
import eve.sys.modular.bom.params.SapAddBomItOrEtParam;
import eve.sys.modular.bom.params.SapAddBomParam;
import eve.sys.modular.bom.params.SapAddBomRespParam;
import eve.sys.modular.bom.params.SapApiParam;
import eve.sys.modular.bom.params.SapBomGetVersion;
import eve.sys.modular.bom.params.SapBomGetVersionReq;
import eve.sys.modular.bom.params.SapBomScanParam;
import eve.sys.modular.bom.params.SapBomScanReqParam;
import eve.sys.modular.bom.params.SapBomScanRespParam;
import eve.sys.modular.bom.params.SapEditBomDcnParamReq;
import eve.sys.modular.bom.params.SapEditBomItOrEtParam;
import eve.sys.modular.bom.params.SapEditBomParam;
import eve.sys.modular.bom.params.SapEditBomRespParam;
import eve.sys.modular.bom.params.TreeBom;
import eve.sys.modular.bom.utils.BomUtils;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SysBomSapServiceUtil {

    public List<SapAddBomParam> loopAddWerksTreeBom(SysBom sysBom, List<TreeBom> treeBoms, String werk) {
        List<SapAddBomParam> sapAddBomParams = new ArrayList<>();
        List<TreeBom> queqeBoms = new ArrayList<>();
        Map<String, Long> mapAdd = new HashMap<>();

        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);

                if (mapAdd.containsKey(tempItem.getId())) {
                    continue;
                }

                SapAddBomParam _param = new SapAddBomParam();
                _param.setIV_CTYPE(sysBom.getBomCtype());
                _param.setIV_UPMEN(300000);// sysBom.getBomUpmen()
                _param.setIT_DATA(new ArrayList<>());
                _param.setIV_WERKS(werk);
                // _param.setIV_STLAL(callGetBomVersion(tempItem,werk));
                _param.setIV_STLAL("");// 版本

                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {

                        _param.getIT_DATA().add(addParam(sysBom, tempItem, _item, werk));

                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {
                                _param.getIT_DATA().add(addParam(sysBom, tempItem, param, werk));
                            }
                        }
                    }
                    mapAdd.put(tempItem.getId(), 0L);
                    queqeBoms.addAll(tempItem.getLists());

                }

                if (!_param.noItData()) {
                    sapAddBomParams.add(_param);
                }
            }
        }

        return sapAddBomParams;
    }

    public List<SapAddBomParam> loopTreeBom(SysBom sysBom, List<TreeBom> treeBoms, String werk) {
        List<SapAddBomParam> sapAddBomParams = new ArrayList<>();
        List<TreeBom> queqeBoms = new ArrayList<>();
        Map<String, Long> mapAdd = new HashMap<>();

        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);

                if (tempItem.getVersion() != null && !tempItem.getVersion().equals("")) {

                    if (!tempItem.isLeaf()) {
                        queqeBoms.addAll(tempItem.getLists());
                    }
                    continue;
                }

                if (mapAdd.containsKey(tempItem.getId())) {
                    continue;
                }

                SapAddBomParam _param = new SapAddBomParam();
                _param.setIV_CTYPE(sysBom.getBomCtype());
                _param.setIV_UPMEN(sysBom.getBomUpmen());
                _param.setIT_DATA(new ArrayList<>());
                _param.setIV_WERKS(werk);
                // _param.setIV_STLAL(callGetBomVersion(tempItem,werk));
                _param.setIV_STLAL("");// 版本

                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {

                        _param.getIT_DATA().add(addParam(sysBom, tempItem, _item, werk));

                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {
                                _param.getIT_DATA().add(addParam(sysBom, tempItem, param, werk));
                            }
                        }
                    }
                    mapAdd.put(tempItem.getId(), 0L);
                    queqeBoms.addAll(tempItem.getLists());

                }

                if (!_param.noItData()) {
                    sapAddBomParams.add(_param);
                }
            }
        }

        return sapAddBomParams;

    }

    public Map<String, TreeBom> loopOldTreeBoms(List<TreeBom> treeBoms, Map<String, TreeBom> flagTreeMap) {

        Map<String, TreeBom> mapTreeBoms = new HashMap<>();

        List<TreeBom> queqeBoms = new ArrayList<>();
        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);
                flagTreeMap.put(tempItem.getId(), tempItem);
                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {
                        mapTreeBoms.put(tempItem.getId() + _item.getId(), _item);
                        flagTreeMap.put(_item.getId(), _item);
                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {
                                mapTreeBoms.put(tempItem.getId() + param.getId(), param);
                                flagTreeMap.put(param.getId(), param);
                            }
                        }
                    }
                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        return mapTreeBoms;
    }

    public List<SapEditBomParam> loopNewTreeBoms(SysBom sysBom, List<TreeBom> treeBoms,
            Map<String, TreeBom> mapTreeBoms, List<TreeBom> newBoms, String werk, Map<String, TreeBom> flagTreeMap) {

        // List<SapEditBomParam> _sapEditBomParams = new ArrayList<>();
        List<SapEditBomParam> sapEditBomParams = new ArrayList<>();
        List<TreeBom> queqeBoms = new ArrayList<>();
        List<String> ids = new ArrayList<String>();

        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);

                if ((null == tempItem.getVersion() || tempItem.getVersion().equals("")) && !tempItem.isLeaf()) {
                    queqeBoms.addAll(tempItem.getLists());
                    newBoms.add(tempItem);
                    continue;
                }

                SapEditBomParam _parm = new SapEditBomParam();
                _parm.setIV_MATNR(tempItem.getSapNumber());
                _parm.setIV_BMENG(1000D);
                _parm.setIT_DATA(new ArrayList<SapEditBomItOrEtParam>());
                _parm.setIV_STLAL(tempItem.getVersion());
                _parm.setIV_WERKS(werk);

                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {

                        addOrEditParam(tempItem, _item, ids, mapTreeBoms, _parm.getIT_DATA());

                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {
                                addOrEditParam(tempItem, param, ids, mapTreeBoms, _parm.getIT_DATA());
                            }
                        }
                    }
                    queqeBoms.addAll(tempItem.getLists());
                }
                for (Map.Entry<String, TreeBom> e : mapTreeBoms.entrySet()) {
                    if (e.getKey().startsWith(tempItem.getId()) && ids.indexOf(e.getKey()) == -1) {
                        SapEditBomItOrEtParam _vo = deleteParams(tempItem, e.getKey(), e.getValue());
                        _parm.getIT_DATA().add(0, _vo);
                        ids.add(e.getKey());
                    }
                }
                if (!_parm.noItData()) {
                    sapEditBomParams.add(_parm);
                }
            }
        }

        Map<String, SapEditBomParam> sapEditBomParamMap = new HashMap<>();

        for (Map.Entry<String, TreeBom> e : mapTreeBoms.entrySet()) {

            if (ids.indexOf(e.getKey()) == -1) {

                TreeBom item = flagTreeMap.get(e.getValue().getParent_id());

                SapEditBomParam _parm = null;

                if (sapEditBomParamMap.containsKey(e.getValue().getParent_id())) {
                    _parm = sapEditBomParamMap.get(e.getValue().getParent_id());
                } else {
                    _parm = new SapEditBomParam();
                    _parm.setIV_MATNR(item.getSapNumber());
                    _parm.setIV_BMENG(1000D);
                    _parm.setIT_DATA(new ArrayList<SapEditBomItOrEtParam>());
                    _parm.setIV_STLAL(item.getVersion());
                    _parm.setIV_WERKS(werk);
                    sapEditBomParamMap.put(e.getValue().getParent_id(), _parm);
                }

                SapEditBomItOrEtParam _vo = deleteParams(item, e.getKey(), e.getValue());
                _parm.getIT_DATA().add(0, _vo);
            }
        }

        for (Map.Entry<String, SapEditBomParam> e : sapEditBomParamMap.entrySet()) {
            if (!e.getValue().noItData()) {
                sapEditBomParams.add(0, e.getValue());
            }
        }

        return sapEditBomParams;
    }

    public List<SapEditBomParam> updateLoopTreeBom(List<TreeBom> treeBoms, String werk) {
        List<SapEditBomParam> sapEditBomParams = new ArrayList<>();
        List<TreeBom> queqeBoms = new ArrayList<>();
        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);

                SapEditBomParam _parm = new SapEditBomParam();
                _parm.setIV_MATNR(tempItem.getSapNumber());
                _parm.setIV_BMENG(1000D);
                _parm.setIT_DATA(new ArrayList<SapEditBomItOrEtParam>());
                _parm.setIV_STLAL(tempItem.getVersion());
                _parm.setIV_STKTX("");
                _parm.setIV_WERKS(werk);

                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {
                        editParam(tempItem, _item, _parm.getIT_DATA());
                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {
                                editParam(tempItem, param, _parm.getIT_DATA());
                            }
                        }
                    }
                    queqeBoms.addAll(tempItem.getLists());
                }
                if (!_parm.noItData()) {
                    sapEditBomParams.add(_parm);
                }
            }
        }
        return sapEditBomParams;
    }

    private void editParam(
            TreeBom item,
            TreeBom _item,
            List<SapEditBomItOrEtParam> IT_DATA) {
        SapEditBomItOrEtParam vo = new SapEditBomItOrEtParam();
        vo.setMPartName(item.getPartName());
        vo.setMPartDescription(item.getPartDescription());
        vo.setPartName(_item.getPartName());
        vo.setPartDescription(_item.getPartDescription());
        vo.setMENGE(_item.getSapPartUse());// division(_item.getPartUse(), item.getPartUse(), 3)
        vo.setPLMITEMNO(_item.getId());
        vo.setIDNRK(_item.getSapNumber());
        vo.setAUSCH(_item.getPartLoss());
        vo.setALPGR(_item.getPartGroup());
        vo.setALPRF(_item.getPartPriority());
        vo.setEWAHR(_item.getPartMaybe());
        vo.setFLDELETE("M");
        vo.setPOSNR(_item.getPosnr());
        vo.setMEINS(_item.getPartUnit());
        IT_DATA.add(vo);
    }

    public Map<String, TreeBom> loopTreeBom2Flat(List<TreeBom> treeBoms) {
        List<TreeBom> queqeBoms = new ArrayList<>();
        queqeBoms.addAll(treeBoms);
        Map<String, TreeBom> mapTreeBom = new HashMap<>();
        while (queqeBoms.size() > 0) {
            TreeBom tempItem = queqeBoms.remove(0);
            mapTreeBom.put(tempItem.getId(), tempItem);
            if (!tempItem.noSub()) {
                for (TreeBom param : tempItem.getSubstitute()) {
                    mapTreeBom.put(param.getId(), param);
                }
            }
            if (!tempItem.isLeaf()) {
                queqeBoms.addAll(tempItem.getLists());
            }
        }
        return mapTreeBom;
    }

    public SapEditBomItOrEtParam deleteParams(TreeBom item, String key, TreeBom _tempItem) {
        SapEditBomItOrEtParam vo = null;
        vo = new SapEditBomItOrEtParam();
        vo.setMPartName(item.getPartName());
        vo.setMPartDescription(item.getPartDescription());
        vo.setPartName(_tempItem.getPartName());
        vo.setPartDescription(_tempItem.getPartDescription());
        vo.setFLDELETE("X");
        vo.setPOSNR(_tempItem.getPosnr());
        // vo.setMENGE(_tempItem.getPartUse());
        vo.setMENGE(_tempItem.getSapPartUse());// division(_tempItem.getPartUse(), item.getPartUse(), 3)
        vo.setALPGR(_tempItem.getPartGroup());
        vo.setIDNRK(_tempItem.getSapNumber());
        vo.setAUSCH(_tempItem.getPartLoss());
        vo.setALPRF(_tempItem.getPartPriority());
        vo.setEWAHR(_tempItem.getPartMaybe());
        vo.setPLMITEMNO(_tempItem.getId());
        _tempItem = null;
        return vo;
    }

    public SapAddBomItOrEtParam addParam(SysBom sysBom, TreeBom tempItem, TreeBom param, String werk) {
        SapAddBomItOrEtParam _newParam = new SapAddBomItOrEtParam();
        _newParam.setMPartName(tempItem.getPartName());
        _newParam.setMPartDescription(tempItem.getPartDescription());
        _newParam.setPartName(param.getPartName());
        _newParam.setPartDescription(param.getPartDescription());
        _newParam.setMATNR(tempItem.getSapNumber());
        _newParam.setDATUV(sysBom.getBomStartdate());
        _newParam.setSTLAN(sysBom.getBomUse());
        _newParam.setBMENG(1000D);// tempItem.getPartUse()
        _newParam.setPOSTP("L");
        _newParam.setPLMITEMNO(param.getId());
        _newParam.setIDNRK(param.getSapNumber());
        // _newParam.setMENGE(param.getPartUse());
        _newParam.setMENGE(param.getSapPartUse());// division(param.getPartUse(), tempItem.getPartUse(), 3)
        _newParam.setMEINS(param.getPartUnit());
        _newParam.setAUSCH(param.getPartLoss() + "");
        _newParam.setALPGR(param.getPartGroup());
        _newParam.setALPRF(param.getPartPriority());
        _newParam.setEWAHR(param.getPartMaybe());
        _newParam.setPOSNR(param.getPosnr());// 新增接口设置行号
        _newParam.setWERKS(werk);
        return _newParam;
    }

    private void addOrEditParam(
            TreeBom item,
            TreeBom _item,
            List<String> ids,
            Map<String, TreeBom> mapTreeBoms,
            List<SapEditBomItOrEtParam> IT_DATA) {

        SapEditBomItOrEtParam vo = new SapEditBomItOrEtParam();
        vo.setMPartName(item.getPartName());
        vo.setMPartDescription(item.getPartDescription());
        vo.setPartName(_item.getPartName());
        vo.setPartDescription(_item.getPartDescription());
        // vo.setMENGE(_item.getPartUse());
        vo.setMENGE(_item.getSapPartUse());// division(_item.getPartUse(), item.getPartUse(), 3)
        vo.setPLMITEMNO(_item.getId());
        vo.setIDNRK(_item.getSapNumber());
        vo.setAUSCH(_item.getPartLoss());
        vo.setALPGR(_item.getPartGroup());
        vo.setALPRF(_item.getPartPriority());
        vo.setEWAHR(_item.getPartMaybe());
        vo.setMEINS(_item.getPartUnit());

        if (StrUtil.isEmpty(_item.getPosnr())) {
            vo.setFLDELETE("A");
            IT_DATA.add(vo);
            ids.add(item.getId() + _item.getId());
            return;
        }
        TreeBom _tempItem = mapTreeBoms.containsKey(item.getId() + _item.getId())
                ? mapTreeBoms.get(item.getId() + _item.getId())
                : null;

        if (_tempItem == null) {

            vo.setFLDELETE("A");
            IT_DATA.add(vo);
            ids.add(item.getId() + _item.getId());
            return;
        }

        if ((_item.toString() + item.getSapPartUse()).equals(_tempItem.toString() + item.getSapPartUse())) {
            ids.add(item.getId() + _item.getId());
            return;
        }
        vo.setFLDELETE("M");
        vo.setPOSNR(_item.getPosnr());
        vo.setMEINS(_item.getPartUnit());
        IT_DATA.add(vo);
        ids.add(item.getId() + _item.getId());
    }

    // 循环获取上个历史的版本号
    public Map<String, String> getVersionMap(List<TreeBom> treeBoms) {
        Map<String, String> versionMap = new HashMap<>();
        List<TreeBom> queqeBoms = new ArrayList<>();
        queqeBoms.addAll(treeBoms);
        while (queqeBoms.size() > 0) {

            TreeBom tempItem = queqeBoms.remove(0);

            versionMap.put(tempItem.getSapNumber(), tempItem.getVersion());

            if (!tempItem.noSub()) {
                for (TreeBom param : tempItem.getSubstitute()) {
                    versionMap.put(param.getSapNumber(), param.getVersion());
                }
            }
            if (!tempItem.isLeaf()) {
                queqeBoms.addAll(tempItem.getLists());
            }
        }

        return versionMap;
    }

    // 循环获取sap的行号和版本号
    public Map<String, String> getSapPosnr(List<SapAddBomRespParam> sapAddBomRespParam,
            Map<String, String> versionMap) {
        Map<String, String> mapPosnr = new HashMap<>();
        sapAddBomRespParam.forEach(e -> {
            e.getET_DATA().forEach(_e -> {
                if (versionMap.containsKey(_e.getMATNR())) {

                    if (!StrUtil.isEmpty(versionMap.get(_e.getMATNR()))) {

                        JSONObject obj = JSONObject.parseObject(versionMap.get(_e.getMATNR()));
                        obj.put(_e.getWERKS(), e.getEV_STLAL());
                        versionMap.put(_e.getMATNR(), obj.toJSONString());

                    } else {

                        JSONObject obj = new JSONObject();
                        obj.put(_e.getWERKS(), e.getEV_STLAL());
                        versionMap.put(_e.getMATNR(), obj.toJSONString());
                    }

                } else {
                    JSONObject obj = new JSONObject();
                    obj.put(_e.getWERKS(), e.getEV_STLAL());
                    versionMap.put(_e.getMATNR(), obj.toJSONString());
                }
                mapPosnr.put(_e.getPLMITEMNO(), _e.getPOSNR());
            });
        });

        return mapPosnr;
    }

    // 循环获取修改bom时的sap的行号
    public void getEditSapPosnr(List<SapEditBomRespParam> sapEditBomRespParams, Map<String, String> mapPosnr) {
        sapEditBomRespParams.forEach(e -> {
            e.getET_DATA().stream().filter(i -> i.getFLDELETE().equals("A")).forEach(_e -> {
                mapPosnr.put(_e.getPLMITEMNO(), _e.getPOSNR());
            });
        });
    }

    // 循环保存行号
    public void loopSavePosnr(List<TreeBom> treeBoms, Map<String, String> mapPosnr, Map<String, String> versionMap) {
        List<TreeBom> queqeBoms = new ArrayList<>();
        queqeBoms.addAll(treeBoms);
        while (queqeBoms.size() > 0) {
            TreeBom tempItem = queqeBoms.remove(0);
            tempItem.setPosnr(
                    mapPosnr.containsKey(tempItem.getId()) ? mapPosnr.get(tempItem.getId()) : tempItem.getPosnr());
            tempItem.setVersion(
                    versionMap.containsKey(tempItem.getSapNumber()) ? versionMap.get(tempItem.getSapNumber())
                            : tempItem.getVersion());

            if (!tempItem.noSub()) {

                tempItem.getSubstitute().forEach(e -> {
                    e.setPosnr(mapPosnr.containsKey(e.getId()) ? mapPosnr.get(e.getId()) : e.getPosnr());
                    e.setVersion(versionMap.containsKey(e.getSapNumber()) ? versionMap.get(e.getSapNumber())
                            : e.getVersion());
                });
            }
            if (!tempItem.isLeaf()) {
                queqeBoms.addAll(tempItem.getLists());
            }
        }
    }

    public void loopSaveSapPartUse(List<TreeBom> treeBoms) {
        List<TreeBom> queqeBoms = new ArrayList<>();
        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {

                TreeBom tempItem = queqeBoms.remove(0);

                if (!tempItem.isLeaf()) {

                    for (TreeBom _item : tempItem.getLists()) {

                        _item.setSapPartUse(division(_item.getPartUse(), tempItem.getPartUse(), 3));

                        if (!_item.noSub()) {

                            for (TreeBom param : _item.getSubstitute()) {
                                param.setSapPartUse(division(param.getPartUse(), tempItem.getPartUse(), 3));
                            }
                        }
                    }

                    queqeBoms.addAll(tempItem.getLists());
                }

            }
        }
    }

    public boolean ifMengeEmpty(List<TreeBom> treeBoms) {
        List<TreeBom> queqeBoms = new ArrayList<>();
        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);
                if (!tempItem.isLeaf()) {
                    for (TreeBom _item : tempItem.getLists()) {
                        if (!_item.isValidate()) {
                            return true;
                        }

                        if (!_item.noSub()) {
                            for (TreeBom param : _item.getSubstitute()) {
                                if (!param.isValidate()) {
                                    return true;
                                }
                            }
                        }
                    }
                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        return false;
    }

    public String callDcnAennr(String jiraNo) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SapEditBomDcnParamReq dcnAennrParam = new SapEditBomDcnParamReq();
        dcnAennrParam.setIV_OAFLW(jiraNo);
        dcnAennrParam.setIV_AETXT("BOM变更");
        dcnAennrParam.setIV_DATUV(sdf.format(new Date()));

        String resp = BomUtils.doPost(SapApiParam.BaseUrl +
                SapApiParam.BomEditDcn,
                SapApiParam.Appkey, JSONObject.toJSONString(dcnAennrParam));
        // System.out.println(resp);

        JSONObject obj = JSONObject.parseObject(resp);

        if (!obj.getString("EV_FLAG").equals("S")) {
            return "";
        }
        return obj.getString("EV_AENNR");
    }

    public String callGetBomVersion(List<String> sapNumbers, String werk) {

        SapBomGetVersionReq req = new SapBomGetVersionReq();
        req.setIT_DATA(new ArrayList<>());
        for (String sapNumber : sapNumbers) {
            req.getIT_DATA().add(SapBomGetVersion.builder().MATNR(sapNumber).WERKS(werk).STLAN("1").build());
        }

        String resp = BomUtils.doPost(SapApiParam.BaseUrl +
                SapApiParam.BomGetVersion,
                SapApiParam.Appkey, JSONObject.toJSONString(req));

        JSONObject obj = JSONObject.parseObject(resp);
        List<SapBomGetVersion> ET_DATA = JSONObject.parseArray(obj.getString("ET_DATA"), SapBomGetVersion.class);

        if (null == ET_DATA || ET_DATA.isEmpty()) {
            return "01";
        }

        List<Long> versions = ET_DATA.stream().map(e -> Long.parseLong(e.getSTLAL())).distinct()
                .collect(Collectors.toList());

        long max = versions.stream().max(Comparator.comparingLong(Long::longValue)).get().longValue() + 1;

        return max >= 10 ? max + "" : "0" + max;
    }

    /*
     * public String callBomVersion(List<String> sapNumbers, String werk) {
     * SapBomGetVersionReq req = new SapBomGetVersionReq();
     * req.setIT_DATA(new ArrayList<>());
     * for (String sapNumber : sapNumbers) {
     * req.getIT_DATA().add(SapBomGetVersion.builder().MATNR(sapNumber).WERKS(werk).
     * STLAN("1").build());
     * }
     * 
     * String resp = BomUtils.doPost(SapApiParam.BaseUrl +
     * SapApiParam.BomGetVersion,
     * SapApiParam.Appkey, JSONObject.toJSONString(req));
     * 
     * JSONObject obj = JSONObject.parseObject(resp);
     * List<SapBomGetVersion> ET_DATA =
     * JSONObject.parseArray(obj.getString("ET_DATA"), SapBomGetVersion.class);
     * 
     * if (null == ET_DATA || ET_DATA.isEmpty()) {
     * throw new ServiceException(500,"sap无记录");
     * }
     * 
     * List<Long> versions = ET_DATA.stream().map(e ->
     * Long.parseLong(e.getSTLAL())).distinct()
     * .collect(Collectors.toList());
     * 
     * long max =
     * versions.stream().max(Comparator.comparingLong(Long::longValue)).get().
     * longValue();
     * 
     * return max + "";
     * }
     */

    public double division(double v1, double v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException("The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(Double.toString(v1));// 子用量
        BigDecimal b2 = new BigDecimal(Double.toString(v2));// 父用量
        BigDecimal b3 = b2.divide(new BigDecimal(1000));// 父用量比例
        return b1.divide(b3, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public double Mutil(double v1, double v2) {
        BigDecimal b1 = new BigDecimal(Double.toString(v1));// 子的sap用量
        BigDecimal b2 = new BigDecimal(Double.toString(v2));// 父用量
        BigDecimal b3 = b2.divide(new BigDecimal(1000));// 父用量比例
        return b1.multiply(b3).doubleValue();
    }

    public TreeBom getTreeBom(List<TreeBom> treeBoms, String id) {
        List<TreeBom> queqeBoms = new ArrayList<>();
        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);
            while (queqeBoms.size() > 0) {
                TreeBom tempItem = queqeBoms.remove(0);
                if (tempItem.getId().equals(id)) {
                    return tempItem;
                }
                if (!tempItem.noSub()) {
                    queqeBoms.addAll(tempItem.getSubstitute());
                }
                if (!tempItem.isLeaf()) {
                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        return null;
    }

    public SapBomScanRespParam callScanBom(String sapNumber, String werk, String version) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String IV_STLAL = version;

        SapBomScanReqParam req = SapBomScanReqParam.builder().IV_CB1("X").IV_WERKS(werk)
                .IV_DATUV(sdf.format(new Date())).IV_MATNR1(sapNumber).IV_STLAL(IV_STLAL).build();

        String resp = BomUtils.doPost(SapApiParam.BaseUrl +
                SapApiParam.BomScan,
                SapApiParam.Appkey, JSONObject.toJSONString(req));
        SapBomScanRespParam res = JSONObject.parseObject(resp, SapBomScanRespParam.class);
        return res;
    }

    public void sapProof(SysBom sysBom, List<TreeBom> treeBoms) throws ServiceException, ParseException {

        List<TreeBom> queqeBoms = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
        Long startDate = sdf.parse(sysBom.getBomStartdate()).getTime();

        // JSONObject obj = JSONObject.parseObject(treeBoms.get(0).getVersion());

        // for (Map.Entry<String, Object> entry : obj.entrySet()) {
        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);

            while (queqeBoms.size() > 0) {

                TreeBom tempItem = queqeBoms.remove(0);

                if (!tempItem.isLeaf() && !StrUtil.isEmpty(tempItem.getVersion())) {

                    JSONObject obj = JSONObject.parseObject(tempItem.getVersion());

                    for (Map.Entry<String, Object> entry : obj.entrySet()) {

                        SapBomScanRespParam resp = callScanBom(tempItem.getSapNumber(), entry.getKey(),
                                entry.getValue() + "");

                        if (resp.getIT_TITLE().size() < 1 || resp.getIT_DATA().size() < 1) {
                            throw new ServiceException(500, "数据导入错误，请审核");
                        }

                        if (!resp.getIT_TITLE().get(0).MATNR()
                                .equals(tempItem.MATNR(entry.getKey(), entry.getValue() + ""))) {
                            throw new ServiceException(500, "sap校验失败：主料号" + tempItem.getSapNumber());
                        }

                        Long Datuv = sdf1.parse(resp.getIT_DATA().get(0).getDATUV()).getTime();

                        if (Datuv > startDate) {
                            throw new ServiceException(500, "sap校验失败,BOM有效期不在sap范围内");
                        }

                        if (tempItem.getLists().size() != resp.getIT_DATA().size()) {

                            throw new ServiceException(500, "sap校验失败：主料号" + tempItem.getSapNumber() + "与sap数量不匹配");
                        }

                        for (TreeBom IDNRK : tempItem.getLists()) {

                            List<String> compareStrings = new ArrayList<>();
                            resp.getIT_DATA().stream().filter(e -> e.getIDNRK().equals(IDNRK.getSapNumber()))
                                    .forEach(_e -> {
                                        compareStrings.add(_e.IDNRK());
                                    });

                            String compareStr = IDNRK.IDNRK(entry.getKey(), entry.getValue() + "");

                            if (compareStrings.indexOf(compareStr) < 0) {
                                throw new ServiceException(500, "sap校验失败：组件料号" + IDNRK.getSapNumber());
                            }
                            /* 替代料sap验证 */
                            /*
                             * if (!IDNRK.noSub()) {
                             * for (TreeBom param : IDNRK.getSubstitute()) {
                             * List<String> compareStrs = new ArrayList<>();
                             * resp.getIT_DATA().stream().filter(e ->
                             * e.getIDNRK().equals(param.getSapNumber()))
                             * .forEach(_e -> {
                             * compareStrs.add(_e.IDNRK());
                             * });
                             * 
                             * String _compareStr = param.IDNRK(entry.getKey(), entry.getValue() + "");
                             * 
                             * if (compareStrs.indexOf(_compareStr) < 0) {
                             * throw new ServiceException(500, "sap校验失败：替代料组件料号" + param.getSapNumber());
                             * }
                             * }
                             * }
                             */
                        }
                    }

                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        // }
    }

    public List<BomSapProof> getSapExendProof(List<TreeBom> treeBoms, List<String> werkNos) {

        List<BomSapProof> proofs = new ArrayList<>();
        List<TreeBom> queqeBoms = new ArrayList<>();

        for (String week : werkNos) {

            for (TreeBom treeBom : treeBoms) {

                queqeBoms.add(treeBom);

                while (queqeBoms.size() > 0) {

                    TreeBom tempItem = queqeBoms.remove(0);

                    if (!tempItem.isValidate()) {
                        throw new ServiceException(500, "BOM使用量不为0");
                    }

                    callBomExtend(
                            tempItem.getSapNumber(),
                            tempItem.getPartUnit(),
                            week,
                            proofs);

                    if (!tempItem.noSub()) {

                        for (TreeBom param : tempItem.getSubstitute()) {

                            callBomExtend(
                                    param.getSapNumber(),
                                    param.getPartUnit(),
                                    week,
                                    proofs);
                        }
                    }

                    if (!tempItem.isLeaf()) {
                        queqeBoms.addAll(tempItem.getLists());
                    }

                }
            }
        }

        return proofs;
    }

    private String padWithZeros(String input, int length) {
        StringBuilder sb = new StringBuilder();
        while (sb.length() + input.length() < length) {
            sb.append('0');
        }
        sb.append(input);
        return sb.toString();
    }

    /* public void callBomExtend(String sapNo, String unit, String week, List<BomSapProof> proofs) {

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("WERKS", week);
        paramMap.put("MATNR", padWithZeros(sapNo, 18));

        JSONObject resp = BomUtils.doGet(
                SapApiParam.BaseUrl + SapApiParam.BomExtendApi+"?WERKS={WERKS}&MATNR={MATNR}",
                SapApiParam.Appkey,
                paramMap);

        if (resp.getBoolean("state")) {
            List<SapBomScanParam> rows = JSONObject.parseArray(resp.getString("rows"), SapBomScanParam.class);

            if (rows.size() < 1) {
                proofs.add(
                        BomSapProof.builder().msg("料号："+sapNo+"未扩充至"+week+"工厂，请联系工厂端采购同事在SAP系统进行料号扩充").build());
            }

            if (rows.size() > 0 && !rows.get(0).getMEINS().equals(unit)) {
                proofs.add(
                        BomSapProof.builder().msg(week + "工厂" + "存在的" + sapNo + "的单位为" + rows.get(0).getMEINS())
                                .build());
            }
        } else {
            throw new ServiceException(500, "sap扩充接口调用失败");
        }
    } */

    public void callBomExtend(String sapNo, String unit, String week, List<BomSapProof> proofs) {

        BomExtendReq bomExtendReq = BomExtendReq.builder()
        .IV_FIELDS("MEINS")
        .IV_TABNAME(SapApiParam.BomExtendView)
        .IV_WHERE("WERKS = '"+ week +"' AND MATNR = '"+ sapNo +"'")
        .build();

        String resp = BomUtils.doPost(SapApiParam.BaseUrl +
        SapApiParam.BomExtendApi,
        SapApiParam.Appkey, JSONObject.toJSONString(bomExtendReq));

        BomExtendResp bomExtendResp = JSONObject.parseObject(resp, BomExtendResp.class);

        if (bomExtendResp.getEV_TYPE().equals("S")) {

            if (bomExtendResp.getEV_ROWS() < 1) {
                proofs.add(
                        BomSapProof.builder().msg("料号："+sapNo+"未扩充至"+week+"工厂，请联系工厂端采购同事在SAP系统进行料号扩充").build());
            }

            if (bomExtendResp.getEV_ROWS() > 0 && !bomExtendResp.getET_RESULT().get(0).getDATA().equals(unit)) {
                proofs.add(
                        BomSapProof.builder().msg(week + "工厂" + "存在的" + sapNo + "的单位为" + bomExtendResp.getET_RESULT().get(0).getDATA())
                                .build());
            }
            
        } else {
            throw new ServiceException(500, "sap扩充接口调用失败");
        }
    }

    public List<BomSapProof> getSapProof(SysBom sysBom, List<TreeBom> treeBoms) throws ParseException {

        List<BomSapProof> proofs = new ArrayList<>();

        List<TreeBom> queqeBoms = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
        Long startDate = sdf.parse(sysBom.getBomStartdate()).getTime();

        // JSONObject obj = JSONObject.parseObject(treeBoms.get(0).getVersion());

        // for (Map.Entry<String, Object> entry : obj.entrySet()) {

        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);

            while (queqeBoms.size() > 0) {

                TreeBom tempItem = queqeBoms.remove(0);

                if (!tempItem.isLeaf() && !StrUtil.isEmpty(tempItem.getVersion())) {

                    JSONObject obj = JSONObject.parseObject(tempItem.getVersion());

                    for (Map.Entry<String, Object> entry : obj.entrySet()) {

                        SapBomScanRespParam resp = callScanBom(tempItem.getSapNumber(), entry.getKey(),
                                entry.getValue() + "");

                        if (resp.getIT_TITLE().size() < 1 || resp.getIT_DATA().size() < 1) {
                            throw new ServiceException(500, "数据导入错误，请审核");
                        }

                        if (!resp.getIT_TITLE().get(0).MATNR()
                                .equals(tempItem.MATNR(entry.getKey(), entry.getValue() + ""))) {
                            List<SapBomScanParam> sapBomScanParams = new ArrayList<SapBomScanParam>(1) {
                                {
                                    add(resp.getIT_TITLE().get(0));
                                }
                            };
                            proofs.add(BomSapProof.builder().treeBom(tempItem).sapScanParam(sapBomScanParams)
                                    .msg("sap校验失败：主料号" + tempItem.getSapNumber()).type(0).werk(entry.getKey())
                                    .version(entry.getValue() + "").build());
                        }

                        Long Datuv = sdf1.parse(resp.getIT_DATA().get(0).getDATUV()).getTime();

                        if (Datuv > startDate) {
                            List<SapBomScanParam> sapBomScanParams = new ArrayList<SapBomScanParam>(1) {
                                {
                                    add(resp.getIT_TITLE().get(0));
                                }
                            };
                            proofs.add(BomSapProof.builder().treeBom(tempItem).sapScanParam(sapBomScanParams)
                                    .msg("sap校验失败,BOM有效期不在sap范围内").type(0).werk(entry.getKey())
                                    .version(entry.getValue() + "").build());
                        }

                        if (tempItem.getLists().size() != resp.getIT_DATA().size()) {
                            List<SapBomScanParam> sapBomScanParams = new ArrayList<SapBomScanParam>(1) {
                                {
                                    add(resp.getIT_TITLE().get(0));
                                }
                            };
                            proofs.add(BomSapProof.builder().treeBom(tempItem).sapScanParam(sapBomScanParams)
                                    .msg("sap校验失败：主料号" + tempItem.getSapNumber() + "与sap数量不匹配").type(0)
                                    .werk(entry.getKey())
                                    .version(entry.getValue() + "").build());
                        }

                        for (TreeBom IDNRK : tempItem.getLists()) {

                            List<String> compareStrings = new ArrayList<>();
                            List<SapBomScanParam> _sapBomScanParams = new ArrayList<>();

                            resp.getIT_DATA().stream().filter(e -> e.getIDNRK().equals(IDNRK.getSapNumber()))
                                    .forEach(_e -> {
                                        compareStrings.add(_e.IDNRK());
                                        _sapBomScanParams.add(_e);
                                    });

                            String compareStr = IDNRK.IDNRK(entry.getKey(), entry.getValue() + "");

                            if (compareStrings.indexOf(compareStr) < 0) {
                                proofs.add(BomSapProof.builder().treeBom(IDNRK).sapScanParam(_sapBomScanParams)
                                        .msg("sap校验失败：组件料号" + IDNRK.getSapNumber()).type(1).werk(entry.getKey())
                                        .version(entry.getValue() + "").build());
                            }
                            /* 替代料sap验证 */
                            /*
                             * if (!IDNRK.noSub()) {
                             * for (TreeBom param : IDNRK.getSubstitute()) {
                             * List<String> compareStrs = new ArrayList<>();
                             * resp.getIT_DATA().stream().filter(e ->
                             * e.getIDNRK().equals(param.getSapNumber()))
                             * .forEach(_e -> {
                             * compareStrs.add(_e.IDNRK());
                             * });
                             * 
                             * String _compareStr = param.IDNRK(entry.getKey(), entry.getValue() + "");
                             * 
                             * if (compareStrs.indexOf(_compareStr) < 0) {
                             * throw new ServiceException(500, "sap校验失败：替代料组件料号" + param.getSapNumber());
                             * }
                             * }
                             * }
                             */
                        }
                    }

                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        // }
        return proofs;
    }

    public void getSapProofs(SysBom sysBom, List<TreeBom> treeBoms, List<BomSapProof> proofLogs,
            List<BomSapProof> proofErrors) throws ParseException {

        List<TreeBom> queqeBoms = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
        Long startDate = sdf.parse(sysBom.getBomStartdate()).getTime();

        // JSONObject obj = JSONObject.parseObject(treeBoms.get(0).getVersion());

        // for (Map.Entry<String, Object> entry : obj.entrySet()) {

        for (TreeBom item : treeBoms) {
            queqeBoms.add(item);

            while (queqeBoms.size() > 0) {

                TreeBom tempItem = queqeBoms.remove(0);

                if (!tempItem.isLeaf() && !StrUtil.isEmpty(tempItem.getVersion())) {

                    JSONObject obj = JSONObject.parseObject(tempItem.getVersion());

                    for (Map.Entry<String, Object> entry : obj.entrySet()) {
                        SapBomScanRespParam resp = callScanBom(tempItem.getSapNumber(), entry.getKey(),
                                entry.getValue() + "");

                        String Matnr = resp.getIT_TITLE().isEmpty() || null == resp.getIT_TITLE() ? ""
                                : resp.getIT_TITLE().get(0).MATNR();

                        List<SapBomScanParam> sapBomScanParams = Matnr.equals("") ? new ArrayList<>()
                                : new ArrayList<SapBomScanParam>(1) {
                                    {
                                        add(resp.getIT_TITLE().get(0));
                                    }
                                };

                        if (!Matnr.equals(tempItem.MATNR(entry.getKey(), entry.getValue() + ""))) {

                            proofErrors.add(BomSapProof.builder().msg("sap校验失败：主料号" + tempItem.getSapNumber())
                                    .treeBom(tempItem.newClone()).sapScanParam(sapBomScanParams).type(0)
                                    .werk(entry.getKey())
                                    .version(entry.getValue() + "").build());
                        } else {

                            proofLogs.add(BomSapProof.builder().msg("sap校验成功：主料号" + tempItem.getSapNumber())
                                    .treeBom(tempItem.newClone()).sapScanParam(sapBomScanParams).type(0)
                                    .werk(entry.getKey())
                                    .version(entry.getValue() + "").build());
                        }

                        Long Datuv = resp.getIT_DATA().isEmpty() || resp.getIT_DATA() == null ? startDate + 1
                                : sdf1.parse(resp.getIT_DATA().get(0).getDATUV()).getTime();

                        if (Datuv > startDate) {

                            proofErrors.add(
                                    BomSapProof.builder().treeBom(tempItem.newClone()).sapScanParam(sapBomScanParams)
                                            .msg("sap校验失败,BOM有效期不在sap范围内").type(0).werk(entry.getKey())
                                            .version(entry.getValue() + "").build());
                        } else {

                            proofLogs.add(
                                    BomSapProof.builder().treeBom(tempItem.newClone()).sapScanParam(sapBomScanParams)
                                            .msg("sap校验成功,BOM有效期在sap范围内").type(0).werk(entry.getKey())
                                            .version(entry.getValue() + "").build());
                        }

                        if (tempItem.getLists().size() != resp.getIT_DATA().size()) {

                            proofErrors.add(
                                    BomSapProof.builder().treeBom(tempItem.newClone()).sapScanParam(sapBomScanParams)
                                            .msg("sap校验失败：主料号" + tempItem.getSapNumber() + "与sap数量不匹配").type(0)
                                            .werk(entry.getKey())
                                            .version(entry.getValue() + "").build());
                        }

                        for (TreeBom IDNRK : tempItem.getLists()) {

                            List<String> compareStrings = new ArrayList<>();
                            List<SapBomScanParam> _sapBomScanParams = new ArrayList<>();

                            resp.getIT_DATA().stream().filter(e -> e.getIDNRK().equals(IDNRK.getSapNumber()))
                                    .forEach(_e -> {
                                        compareStrings.add(_e.IDNRK());
                                        _sapBomScanParams.add(_e);
                                    });

                            String compareStr = IDNRK.IDNRK(entry.getKey(), entry.getValue() + "");

                            if (compareStrings.indexOf(compareStr) < 0) {

                                proofErrors.add(
                                        BomSapProof.builder().treeBom(IDNRK.newClone()).sapScanParam(_sapBomScanParams)
                                                .msg("sap校验失败：组件料号" + IDNRK.getSapNumber()).type(1).werk(entry.getKey())
                                                .version(entry.getValue() + "").build());
                            } else {
                                proofLogs.add(
                                        BomSapProof.builder().treeBom(IDNRK.newClone()).sapScanParam(_sapBomScanParams)
                                                .msg("sap校验成功：组件料号" + IDNRK.getSapNumber()).type(1).werk(entry.getKey())
                                                .version(entry.getValue() + "").build());
                            }
                            /* 替代料sap验证 */
                            /*
                             * if (!IDNRK.noSub()) {
                             * for (TreeBom param : IDNRK.getSubstitute()) {
                             * List<String> compareStrs = new ArrayList<>();
                             * resp.getIT_DATA().stream().filter(e ->
                             * e.getIDNRK().equals(param.getSapNumber()))
                             * .forEach(_e -> {
                             * compareStrs.add(_e.IDNRK());
                             * });
                             * 
                             * String _compareStr = param.IDNRK(entry.getKey(), entry.getValue() + "");
                             * 
                             * if (compareStrs.indexOf(_compareStr) < 0) {
                             * throw new ServiceException(500, "sap校验失败：替代料组件料号" + param.getSapNumber());
                             * }
                             * }
                             * }
                             */
                        }
                    }
                    queqeBoms.addAll(tempItem.getLists());
                }
            }
        }
        // }
    }

}
