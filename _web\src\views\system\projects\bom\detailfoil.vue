<template>
    <div style="position: relative;">
        <div class="add-btn" v-if="activeKey != '1'">
            <a-button size="small" @click="bomFileShow = true" style="margin-right: 8px;">操作指引</a-button>
            <a-button size="small" type="primary" @click="toAddBom">搭建{{btnBom[bomType]}}BOM</a-button>
        </div>
        <blockTabsIndex
            :pageLevel="1" 
            :pageTitleShow="false" 
            :tabsList="tabsList"  
            :activeKey="activeKey" 
            :tabsFontSize="13"
            @tabClick="callback"  
            @onEdit="onEdit" 
            :tableTotal="dataSource.length"  
            :loading="loading"  
            :paginationShow = "false"
        >
            <!-- <template #search>
                <pbiSearchContainer>
                    <pbiSearchItem type='btn' :span="24" v-if="activeKey != '1'">
                        <div class="main-btn">
                            <a-button type="primary" size="small" @click="toAddBom">搭建{{btnBom[bomType]}}BOM</a-button>
                        </div>
                    </pbiSearchItem>
                </pbiSearchContainer>
            </template> -->
            <!-- 表格 -->
            <template #table>
                <ag-grid-vue
                    v-if="activeKey != '1'"
                    :style='`height: ${tableHeight}px`' 
                    :tooltipShowDelay="0"
                    class="table ag-theme-balham"
                    :columnDefs='columnDefs'
                    :rowData='dataSource' 
                    :grid-options="gridOptions"
                    :suppressDragLeaveHidesColumns="true"
                    :suppressMoveWhenColumnDragging="true"
                >
                </ag-grid-vue>
                <bom v-else @closeBom="closeBom" :issueId="issueId" :projectdetail="projectdetail" :bomId="bomId" :bomType="parseInt(bomType)"/>
            </template>
        </blockTabsIndex>
        <checkhistory2 ref="checkhistory2"/>
        <bomhistory ref="bomhistory" />
        <bomaddwerk :werklines="werklines" ref="bomaddwerk" @ok="getBomList"/>
        <bomselect :werklines="werklines" ref="bomselect" @ok="getBomList"/>
        <addmultiwerk :werklines="werklines" ref="addmultiwerk" @ok="getBomList"/>
        <bomend :dataLines="dataLines" ref="bomend" />
        <upgrade ref="upgrade" @callBomUpgrade="sysBomUpgrade" />
        <bomverify ref="bomverify" />
        <bomupgrade ref="bomupgrade" @updateVis="getBomList" />
        <a-drawer :bodyStyle="{ height: '100%' }" placement="right" :closable="false" width="80%" :visible="visible" @close="visible = false">
            <iframe :src="pdfUrl+'#view=FitH,top'" width="100%" height="100%"></iframe>
        </a-drawer>
        <bomFile v-if="bomFileShow" @close="bomFileShow = false"></bomFile>
    </div>
</template>

<script>
    import store from '@/store'
    import bom from './foilbom'
    import checkhistory2 from './checkhistory2'
    import bomhistory from './bomhistory'
    import bomaddwerk from './bomaddwerk'
    import bomselect from './bomselect'
    import addmultiwerk from './addmultiwerk'
    import bomend from './bomend'
    import upgrade from './upgrade'
    import bomverify from './bomverify'
    import bomupgrade from './bomupgrade'
    import bomFile from './modal/bomFile.vue'
    import Vue from 'vue'
    import {
        
        getwerklines,
        getBomList,
        sysBomDel,
        sysBomUpgrade,
        sapVerify,
        adminConfirm,
        pdfUpdate,
        sysBomWidthDraw,
        sysBomwithDrawBomlines
    } from "@/api/modular/system/bomManage"

    export default {
        name: 'product_detail',
        props: {
            

            
        },
        components: {
            bom,
            bomFile,
            checkhistory2,
            bomhistory,
            bomaddwerk,
            bomselect,
            addmultiwerk,
            bomend,
            upgrade,
            bomverify,
            bomupgrade,
            linesCellRender:{
                template: `
                <div>
                    <div v-if="params.value && params.value.length > 0">
                        {{params.onLinesRenderer(params)}}
                    </div>
                    <div v-else></div>
                </div>`
            },
            checkHistoryCellRender:{
                template: `
                <span>
                    <a @click="params.onOpen(params)">
                        查阅
                    </a>
                </span>`
            },
            historyCellRender:{
                template: `
                <span>
                    <a @click="params.onOpen(params)">
                        查阅
                    </a>
                </span>`
            },
            werkCellRender:{
                template: `
                    <div>
                        <template v-if="(!params.data.isCheck && params.data.bomStatus == 0) || params.data.bomStatus == 4 || params.data.bomStatus == 2">
                            <a-dropdown placement="bottomCenter" overlayClassName='product_dropdown'>
                                <a-button style="font-size:12px" type="link" size='small'> 工厂维护 <a-icon type="down" /></a-button>
                                <a-menu slot="overlay">
                                    <a-menu-item >
                                        <a @click="params.onOpen(params)" style="font-size: 12px;">
                                            {{params.data.lines && params.data.lines.length > 0 ? '新增' : '设置'}}工厂
                                        </a>
                                    </a-menu-item>
                                    <a-menu-item>
                                        <a v-if="params.data.lines.length > 1 || !params.data.isCheck" @click="params.onDelWerk(params)" style="font-size: 12px;">删除工厂</a>
                                        <a v-else :style="{cursor:'not-allowed'}" style="font-size: 12px;">删除工厂</a>
                                    </a-menu-item>

                                    <a-menu-item >
                                        <a @click="params.onAddWerk(params)" style="font-size: 12px;">重复选择产线</a>
                                    </a-menu-item>

                                </a-menu>
                            </a-dropdown>
                        </template>
                        <a-button style="font-size:12px" v-else type="link" size='small' disabled> 工厂维护 <a-icon type="down" /></a-button>
                    </div>
                `
            },
            packBomRender:{
                template: `
                    <span>
                        <a @click="params.onBomEnd(params)">
                            {{params.data.bomEndCount <= 0 ? '关联' : '已关联'}}
                        </a>
                    </span>
                `
            },
            bomNoCellRender:{
                template: `
                    <a @click="params.previewPdfs(params)">{{params.value}}</a>
                `
            },
            actionRender:{
                template: `
                <div>
                    <a-dropdown placement="bottomCenter" overlayClassName='product_dropdown'>
                        <a-button style="font-size:12px" type="link" size='small'> 更多 <a-icon type="down" /></a-button>
                        <a-menu v-if="params.data.bomStatus == 1 || params.data.bomStatus == 3" slot="overlay">
                            <a-menu-item>
                                <a @click="params.onEdit(params)">查看</a>
                            </a-menu-item>
                        </a-menu>
                        <a-menu v-if="params.data.bomStatus == 0 || params.data.bomStatus == 4" slot="overlay">
                            <a-menu-item>
                                <a @click="params.onEdit(params)" style="font-size: 12px;">编辑</a>
                            </a-menu-item>
                            <a-menu-item>
                                <a @click="params.onOa(params)" style="font-size: 12px;">提交</a>
                            </a-menu-item>
                            <a-menu-item>
                                <a @click="params.onWithdraw(params)" style="font-size: 12px;">撤回</a>
                            </a-menu-item>
                            <a-menu-item v-if="(params.data.bomStatus == 0 || params.data.bomStatus == 4) && !params.data.isCheck">
                                <a @click="params.onDelBom(params)" style="font-size: 12px;">删除</a>
                            </a-menu-item>
                            <a-menu-item v-if="params.isAdmin()">
                                <a-popconfirm placement="topRight" title="确认升版？" @confirm="() => params.onAdminConfirm(params)">
                                    <a style="font-size: 12px;">确认升版</a>
                                </a-popconfirm>
                            </a-menu-item>
                        </a-menu>
                        <a-menu v-if="params.data.bomStatus == 2" slot="overlay">
                            <a-menu-item>
                                <a @click="params.onEdit(params)" style="font-size: 12px;">查看</a>
                            </a-menu-item>
                            <a-menu-item>
                                <a @click="params.onShowConfirm(params)" style="font-size: 12px;">修订</a>
                            </a-menu-item>
                            <a-menu-item v-if="params.data.productState >= 4">
                                <a @click="params.onSapVerify(params)" style="font-size: 12px;">sap校验</a>
                            </a-menu-item>
                        </a-menu>
                        <a-menu v-if="params.data.bomStatus == 5 || params.data.bomStatus == 6" slot="overlay">
                            <a-menu-item>
                                <a @click="params.onEdit(params)" style="font-size: 12px;">查看</a>
                            </a-menu-item>
                            <a-menu-item>
                                <a @click="params.onWithdrawLines(params)" style="font-size: 12px;">撤回</a>
                            </a-menu-item>
                            <a-menu-item>
                                <a @click="params.onOa(params)" style="font-size: 12px;">提交</a>
                            </a-menu-item>
                            <a-menu-item v-if="params.isAdmin() && params.data.bomStatus == 5">
                                <a-popconfirm placement="topRight" title="确认升版？" @confirm="() => params.onAdminConfirm(params)">
                                    <a style="font-size: 12px;">确认升版</a>
                                </a-popconfirm>
                            </a-menu-item>
                        </a-menu> 
                        <a-menu v-if="params.data.bomStatus == 7" slot="overlay">
                            <template v-if="hasPerm('sysBom:copy')">
                                <a-menu-item>
                                    <a @click="params.onEdit(params)" style="font-size: 12px;">编辑</a>
                                </a-menu-item>
                                <a-menu-item>
                                    <a-popconfirm placement="topRight" title="确认已经导入？"
                                        @confirm="() => params.onUpgrade(params)">
                                        <a style="font-size: 12px;">确认导入</a>
                                    </a-popconfirm>
                                </a-menu-item>
                            </template>
                            <template v-else>
                                <a-menu-item>
                                    <a @click="params.onEdit(params)" style="font-size: 12px;">查看</a>
                                </a-menu-item>
                            </template>
                        </a-menu>
                    </a-dropdown>
                </div>
                `
            }
        },
        data() {
            return {
                issueId: 0,
                projectdetail: {
                    productProjectName: 'CG01',
                    issueId:0,
                    productState: 6
                },
                pdfUrl: '',
                //admin_type: false,
                tableHeight: document.documentElement.clientHeight - 40 - 16 - 36 - 16,
                visible: false,
                dataSource: [],
                btnBom:{
                    '4': '涂覆铝箔',
                    '5': '涂覆铜箔',
                },
                tabsList: [
                    {
                        value: '4',
                        label: '涂覆铝箔',
                        closable: false
                    },
                    /* {
                        value: '5',
                        label: '涂覆铜箔',
                        closable: false
                    }, */
                    {
                        value: '1',
                        label: '搭建BOM',
                        closable: true,
                        show: false
                    },
                ],
                columnDefs:[
                    {   
                        headerName: '序号', 
                        field: 'no' , 
                        width: 50,
                        cellRenderer: function (params) {
                            return parseInt(params.node.id) + 1
                        },
                    },
                    {   
                        headerName: '箔材名称', 
                        field: 'producttName', 
                        cellRenderer: this.producttNameCellRenderer,
                    },
                    {   
                        headerName: '箔材物料代码', 
                        field: 'bomData', 
                        width: 110,
                        cellRenderer: function (params) {
                            return params.value.length > 2 ? JSON.parse(params.value)[0].sapNumber : ''
                        },
                    },
                    {   
                        headerName: '文件编号', 
                        field: 'bomNo', 
                        cellRenderer: 'bomNoCellRender',
                        minWidth:235,
                        cellRendererParams:{previewPdfs:this.pdfUpdate}
                    },
                    {
                        headerName: '适用工厂', 
                        field: 'lines', 
                        cellRenderer: 'linesCellRender',
                        cellRendererParams: { onLinesRenderer: this.linesRenderer },
                        tooltipValueGetter: (p) =>  this.linesRenderer(p), 
                    },
                    {
                        headerName: '流程审批', 
                        field: 'checkhistory', 
                        width: 75,
                        cellRenderer: 'checkHistoryCellRender',
                        cellRendererParams: { onOpen: this.checkHistoryRenderer },
                    },
                    {
                        headerName: '变更履历', 
                        field: 'history', 
                        width: 75,
                        cellRenderer: 'historyCellRender',
                        cellRendererParams: { onOpen: this.historyRenderer },
                    },
                    {
                        headerName: '操作', 
                        field: 'action', 
                        width: 85,
                        cellRenderer: 'actionRender',
                        cellRendererParams: { 
                            onEdit: this.editBom,
                            onOa: this.toOa,
                            onShowConfirm: this.showConfirm,
                            onSapVerify: this.sapVerify,
                            onUpgrade: this.upgrade,
                            onWithdraw: this.sysBomWidthDraw,
                            onWithdrawLines: this.sysBomwithDrawBomlines,
                            onAdminConfirm: this.adminConfirm,
                            onDelBom: this.sysBomDel,
                            isAdmin: this.isAdmin
                        },

                    },
                    {
                        headerName: '工厂维护', 
                        field: 'werk', 
                        width: 110,
                        cellRenderer: 'werkCellRender',
                        cellRendererParams: {  onOpen: this.werkRender, onDelWerk: this.delWerkRender, onAddWerk: this.addmultiwerk },
                    },
                    /* {
                        headerName: '关联包装BOM', 
                        field: 'packBom', 
                        width: 120,
                        minWidth:120,
                        cellRenderer: 'packBomRender',
                        cellRendererParams: { onBomEnd: this.toBomEnd },
                    }, */
                ],
                activeKey:'4',
                bomType: '4',
                loading: false,
                bomId:null,

                werklines: {},
                dataLines: {},
                gridOptions:{
                    onGridReady: (event) => {
                        event.api.sizeColumnsToFit();
                    }
                },
                bomFileShow:false,
            }
        },
        methods: {
            previewPdf(id) {
                this.pdfUrl = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token')+'&id=' + id
                this.visible = true
			},
            onEdit(key) {
                if (key != '1') {
                    return false;
                }
                let show = this.tabsList.find(item => item.value == key).show 
                show = !show
                this.tabsList.find(item => item.value == key).show = show
                if(!show){
                    this.activeKey = this.bomType
                }else{
                    this.activeKey = key
                }
            },
            closeBom(){
                this.activeKey = this.bomType
                this.bomId = null
                this.tabsList.find(item => item.value == '1').show = false
                this.getBomList()
            },
            pdfUpdate(params) {
                if (params.data.bomData.length < 3) {
                    this.$message.error("请先搭建bom");
                    return false
                }
                this.loading = true
                pdfUpdate({
                    id: params.data.id
                }).then((res) => {
                    if (res.success) {
                        this.pdfUrl = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get('Access-Token')+'&id=' + res.data
                        this.visible = true
                    }
                }).finally(() => {
                    this.loading = false
                })
            },
            sysBomwithDrawBomlines(params){
                this.loading = true
                sysBomwithDrawBomlines({
                    id:params.data.id
                }).then((res) => {
                    if (res.success) {
                        this.$message.info('已撤回', 1);
                        this.getBomList()
                    } else {
                        this.$message.error(res.message, 1);
                    }
                }).finally((res) => {
                    this.loading = false
                })
            },
            sysBomWidthDraw(params){
                this.loading = true
                sysBomWidthDraw({
                    id:params.data.id
                }).then((res) => {
                    if (res.success) {
                        this.$message.info('已撤回', 1);
                        this.getBomList()
                    } else {
                        this.$message.error(res.message, 1);
                    }
                }).finally((res) => {
                    this.loading = false
                })
            },
            sysBomDel(params) {
                this.loading = true
                sysBomDel({
                    id: params.data.id
                })
                .then((res) => {
                    if (res.success) {
                        this.$message.info('删除成功', 1);
                        this.getBomList()
                    } else {
                        this.$message.error(res.message, 1);
                    }
                }).finally(() => {
                    this.loading = false
                })
            },
            editBom(params) {
                this.activeKey = '1'
                this.bomId = params.data.id
                this.tabsList.find(item => item.value == this.activeKey).label = `编辑${this.btnBom[this.bomType]}BOM`
                this.tabsList.find(item => item.value == this.activeKey).show = true
            },
            toAddBom(){
                this.activeKey = '1'
                this.bomId = null
                this.tabsList.find(item => item.value == this.activeKey).label = `搭建${this.btnBom[this.bomType]}BOM`
                this.tabsList.find(item => item.value == this.activeKey).show = true
            },
            toOa(params){
                this.$refs.bomupgrade.edit(params.data)
                return
            },
            adminConfirm(params) {
                this.loading = true
                adminConfirm({
                    id: params.data.id
                }).then((res) => {
                    if (res.success) {
                        this.$message.info('操作成功', 1);
                    } else {
                        this.$message.error(res.message, 1);
                    }
                }).finally(() => {
                    this.loading = false
                })
            },
            sapVerify(params) {
                this.loading = true
                sapVerify({
                    id: params.data.id
                }).then((res) => {
                    if (res.success) {
                        if (res.data.length < 1 || !res.data) {
                            this.$message.success('sap校验成功');
                        } else {
                            this.$refs.bomverify.view(res.data)
                        }
                    } else {
                        this.$message.error(res.message, 1);
                    }
                }).finally(() => {
                    this.loading = false
                })
            },
            sysBomUpgrade(_param) {
                this.loading = true
                sysBomUpgrade({
                    id: _param.id,
                    bomVersion: _param.bomVersion,
                    fileId: _param.fileId
                }).then((res) => {
                    if (res.success) {
                        this.$message.success('请点击编辑按钮进入搭建页面修改BOM物料信息')
                        this.getBomList()
                    }else{
                        this.$message.error('修订失败：' + res.message)
                    }
                }).finally(() => {
                    this.loading = false
                })
            },
            showConfirm(params) {
                let that = this
                that.$confirm({
                    title: '请确认是否修订?',
                    onOk() {
                        that.sysBomUpgrade(params.data)
                    },
                    onCancel() {
                    },
                    class: 'test',
                });
            },
            upgrade(params){
                this.$refs.upgrade.edit(params.data)
            },
            toBomEnd(params) {
                if (params.data.lines.length < 1) {
                    this.$message.error('请先关联产线')
                    return
                }
                this.$refs.bomend.edit(params.data)
            },
            addmultiwerk(params){
                this.$refs.addmultiwerk.edit(params.data)
            },
            
            delWerkRender(params){
                this.$refs.bomselect.edit(params.data)
            },
            werkRender(params){
                this.$refs.bomaddwerk.edit(params.data)
            },
            historyRenderer(params){
                this.$refs.bomhistory.edit(parseInt(this.bomType),this.projectdetail,params.data)
            },
            checkHistoryRenderer(params){
                this.$refs.checkhistory2.edit(parseInt(this.bomType),params.data)
            },
            producttNameCellRenderer (params) {
                return this.projectdetail.productProjectName
            },
            linesRenderer(params){
                return params.value.map(item => this.dataLines[item]).join(';')
            },
            callback(val) { 
                this.activeKey = val
                if (val != '1'){
                    this.bomType = val
                    this.getBomList()
                }
            },
            getBomList(){
                this.loading = true
                let params = {
                    bomIssueId: this.issueId,
                    bomType: this.bomType
                }
                getBomList(params).then((res)=>{
                    if (res.success) {
                        this.dataSource = res.data
                    }
                }).finally(()=>{
                    this.loading = false
                })
            },
            getwerklines() {
                this.loading = true
                getwerklines({werkType: 2}).then(res=>{
                    if (res.success) {
                        let _map = {}
                        for (const key in res.data) {
                            for (const item of res.data[key]) {
                                _map[item.id] = item.werkNo + ( item.lineName && item.lineName.length > 0 ? '->' + item.lineName : '')
                            }
                        }
                        this.dataLines = _map
                        this.werklines = res.data;
                    }

                }).finally(()=>{
                    this.loading = false
                })
            },

            isAdmin(){
                return store.getters.admintype == '1'
            }
        },
        
        created() {
            //this.admin_type = store.getters.admintype == '1'
            this.getwerklines()
            this.getBomList()
        }
    }
</script>
<style lang='less' scoped=''>
    ::v-deep .ant-tabs-bar{
        margin:0
    }
    .add-btn{
        position: absolute;
        top: 4px;
        right: 10px;
        z-index: 1;
        ::v-deep .ant-btn{
            font-size: 12px;
            // height: 26px;
            border-radius: 4px;
        }
    }
    .product_dropdown {
        .ant-dropdown-menu{
            text-align: center;
            padding: 0;
        }
        .ant-dropdown-menu-item{
            font-size: 12px;
            padding: 2px 6px;
            border-bottom: 1px solid #e9e9e9;
        }
        .ant-dropdown-menu-item > a{
            padding: 0;
            margin: 0;
            color: #000;
        }
    }
</style>